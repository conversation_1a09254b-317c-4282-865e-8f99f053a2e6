#!/bin/bash

# 最简单的Docker部署脚本

echo "🚀 开始部署后端服务..."

# 停止并删除旧容器
echo "🛑 停止旧容器..."
docker stop mail-aggregator || true
docker rm mail-aggregator || true

# 进入后端目录构建镜像
echo "🏗️ 构建镜像..."
cd backend
docker build -t mail-aggregator .

# 创建数据目录
echo "📁 创建数据目录..."
mkdir -p data logs

# 运行容器
echo "🚀 启动容器..."
docker run -d \
  --name mail-aggregator \
  -p 8888:8888 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -e FLASK_ENV=production \
  -e SECRET_KEY=8f591yB_e5VwLpYd6a3h3oT3nQ1wP8qL-X9gV7bKz0c \
  -e FRONTEND_URL=https://mail.jx099.com \
  -e PORT=8888 \
  --restart unless-stopped \
  mail-aggregator

# 检查容器是否启动
echo "🔍 检查容器状态..."
if docker ps | grep -q mail-aggregator; then
    echo "✅ 容器启动成功"
else
    echo "❌ 容器启动失败，查看日志:"
    docker logs mail-aggregator
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
for i in {1..30}; do
    if curl -f http://localhost:8888/health > /dev/null 2>&1; then
        echo "✅ 部署成功！服务运行正常"
        echo "🌐 服务地址: http://localhost:8888"
        echo "📊 健康检查: http://localhost:8888/health"
        echo "🎉 部署完成！"
        exit 0
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

echo "❌ 服务启动超时，请检查日志:"
echo "docker logs mail-aggregator"
docker logs mail-aggregator --tail 50
