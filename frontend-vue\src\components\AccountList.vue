<template>
  <section class="panel-section">
    <h2 class="section-title">
      📧 账户选择
      <span class="account-count">({{ accounts.length }}个)</span>
    </h2>
    
    <div class="accounts-container">
      <!-- 全选账户选项 -->
      <div class="account-item all-accounts">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            ref="selectAllCheckbox"
            class="account-checkbox"
            @change="handleSelectAll"
          >
          <span class="checkbox-custom"></span>
          <span class="account-text">
            <strong>所有账户</strong>
          </span>
        </label>
      </div>
      
      <!-- 账户列表容器 -->
      <div class="accounts-list">
        <div v-if="loading" class="loading-placeholder">
          <span class="loading-spinner">🔄</span>
          <span>正在加载账户...</span>
        </div>
        
        <div v-else-if="accounts.length === 0" class="empty-state">
          <div class="empty-icon">📭</div>
          <div class="empty-title">暂无账户</div>
          <div class="empty-description">请先添加邮箱账户</div>
        </div>
        
        <div 
          v-else
          v-for="account in paginatedAccounts" 
          :key="account.id" 
          class="account-item"
        >
          <label class="checkbox-label">
            <input
              type="checkbox"
              class="account-checkbox"
              :value="account.email_address"
              v-model="selectedAccounts"
              @change="handleAccountChange"
            >
            <span class="checkbox-custom"></span>
            <span class="account-text">
              {{ account.email_address }}
              <span :class="['account-status', account.status.toLowerCase().replace(' ', '-')]">
                {{ account.status }}
              </span>
            </span>
          </label>
                     <button
             type="button"
             class="btn-delete"
             :data-account-email="account.email_address"
             title="删除账户"
             @click="handleDeleteAccount(account)"
           >
             ✕
           </button>
        </div>
      </div>
    </div>

    <!-- 批量操作区域 -->
    <div v-if="selectedAccounts.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedAccounts.length }} 个账户
      </div>
      <div class="batch-buttons">
        <button 
          type="button" 
          class="btn btn-danger btn-small"
          @click="handleBatchDelete"
          :disabled="isDeleting"
        >
          <span class="btn-icon">{{ isDeleting ? '⏳' : '🗑️' }}</span>
          <span class="btn-text">{{ isDeleting ? '删除中...' : '批量删除' }}</span>
        </button>
        <button 
          type="button" 
          class="btn btn-warning btn-small"
          @click="handleClearAll"
          :disabled="isDeleting"
        >
          <span class="btn-icon">🧹</span>
          <span class="btn-text">清空所有</span>
        </button>
      </div>

      <!-- 分页控件 -->
      <div class="account-pagination" v-if="totalPages > 1">
        <div class="pagination-info">
          第{{ currentPage }}/{{ totalPages }}页，共{{ totalAccounts }}个账户
        </div>
        
        <div class="pagination-controls">
          <button 
            class="btn btn-small btn-secondary" 
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            ⬅️
          </button>
          
          <div class="page-numbers">
            <button 
              v-for="page in Math.min(totalPages, 5)" 
              :key="page"
              :class="['btn', 'btn-small', page === currentPage ? 'btn-primary' : 'btn-secondary']"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>
          
          <button 
            class="btn btn-small btn-secondary" 
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            ➡️
          </button>
        </div>

        <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
          <option value="15">15个/页</option>
          <option value="20">20个/页</option>
          <option value="30">30个/页</option>
          <option value="40">40个/页</option>
          <option value="50">50个/页</option>
          <option value="100">100个/页</option>
        </select>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, watch, nextTick, computed } from 'vue'

const props = defineProps({
  accounts: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'delete-account', 'batch-delete', 'clear-all'])

const selectedAccounts = ref([...props.modelValue])
const selectAllCheckbox = ref(null)
const isDeleting = ref(false)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20) // 每页显示20个账户

// 分页计算
const totalAccounts = computed(() => props.accounts.length)
const totalPages = computed(() => Math.ceil(totalAccounts.value / pageSize.value))
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value)
const endIndex = computed(() => Math.min(startIndex.value + pageSize.value, totalAccounts.value))

const paginatedAccounts = computed(() => {
  return props.accounts.slice(startIndex.value, endIndex.value)
})

// 分页控制
const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const handlePageSizeChange = () => {
  pageSize.value = parseInt(pageSize.value)
  currentPage.value = 1
}

// 监听选中账户变化
watch(selectedAccounts, (newValue) => {
  emit('update:modelValue', newValue)
  updateSelectAllState()
}, { deep: true })

// 监听外部传入的选中账户变化
watch(() => props.modelValue, (newValue) => {
  // 避免无限递归：只有当值真正不同时才更新
  const currentValue = JSON.stringify(selectedAccounts.value.sort())
  const newValueStr = JSON.stringify([...newValue].sort())
  if (currentValue !== newValueStr) {
    selectedAccounts.value = [...newValue]
  }
}, { deep: true })

// 更新全选状态
const updateSelectAllState = async () => {
  await nextTick()
  if (!selectAllCheckbox.value) return
  
  const totalCount = props.accounts.length
  const checkedCount = selectedAccounts.value.length
  
  if (checkedCount === 0) {
    selectAllCheckbox.value.indeterminate = false
    selectAllCheckbox.value.checked = false
  } else if (checkedCount === totalCount) {
    selectAllCheckbox.value.indeterminate = false
    selectAllCheckbox.value.checked = true
  } else {
    selectAllCheckbox.value.indeterminate = true
    selectAllCheckbox.value.checked = false
  }
}

// 处理全选
const handleSelectAll = (event) => {
  const isChecked = event.target.checked
  if (isChecked) {
    selectedAccounts.value = props.accounts.map(account => account.email_address)
  } else {
    selectedAccounts.value = []
  }
}

// 处理单个账户选择
const handleAccountChange = () => {
  updateSelectAllState()
}

// 处理删除账户
const handleDeleteAccount = async (account) => {
  const confirmed = confirm(`确定要删除账户 "${account.email_address}" 吗？\n\n此操作不可撤销。`)
  if (confirmed) {
    emit('delete-account', account)
  }
}

// 处理批量删除
const handleBatchDelete = async () => {
  if (selectedAccounts.value.length === 0) {
    return
  }
  
  const confirmed = confirm(`确定要删除选中的 ${selectedAccounts.value.length} 个账户吗？\n\n此操作不可撤销。`)
  if (!confirmed) {
    return
  }
  
  isDeleting.value = true
  
  try {
    // 获取要删除的账户对象
    const accountsToDelete = props.accounts.filter(account => 
      selectedAccounts.value.includes(account.email_address)
    )
    
    emit('batch-delete', accountsToDelete)
  } finally {
    isDeleting.value = false
  }
}

// 处理清空所有账户
const handleClearAll = async () => {
  const confirmed = confirm(`确定要删除所有 ${props.accounts.length} 个账户吗？\n\n此操作不可撤销，将清空所有账户数据。`)
  if (!confirmed) {
    return
  }
  
  isDeleting.value = true
  
  try {
    emit('clear-all', props.accounts)
  } finally {
    isDeleting.value = false
  }
}

// 监听账户列表变化，更新全选状态
watch(() => props.accounts, () => {
  updateSelectAllState()
}, { deep: true })
</script>

<style scoped>
.account-status {
  font-size: 0.7rem;
  padding: 1px 4px;
  border-radius: 3px;
  margin-left: 6px;
  font-weight: 500;
  display: inline-block;
  line-height: 1.2;
}

.account-status.ok {
  background-color: #dcfce7;
  color: #166534;
}

.account-status.token-invalid {
  background-color: #fef2f2;
  color: #dc2626;
}

.account-status.login-failed {
  background-color: #fff7ed;
  color: #ea580c;
}

.account-status.error {
  background-color: #fef2f2;
  color: #dc2626;
}

.account-status.unknown {
  background-color: #f1f5f9;
  color: #64748b;
}

/* 账户容器布局 */
.accounts-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 3px;
  max-height: 600px;
  overflow-y: auto;
  padding: 6px;
}

/* 账户项样式 */
.account-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-primary);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  min-height: 38px;
}

.account-item:hover {
  background: var(--bg-hover, #f8fafc);
  border-color: var(--primary-color, #3b82f6);
}

.account-item.selected {
  background: var(--primary-color-light, #dbeafe);
  border-color: var(--primary-color, #3b82f6);
}

/* 复选框样式优化 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.account-checkbox {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
}

.checkbox-custom {
  display: none; /* 隐藏自定义复选框，使用原生样式 */
}

/* 账户项布局修复 */
.account-item .checkbox-label {
  flex: 1;
  min-width: 0;
}

.account-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
  min-width: 0;
  line-height: 1.3;
}

.account-text > span:first-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 删除按钮优化 */
.btn-delete {
  padding: 4px 6px;
  font-size: 0.75rem;
  min-height: 28px;
  min-width: 28px;
  border: 1px solid #dc2626;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-delete:hover {
  background: #dc2626;
  color: white;
  transform: scale(1.05);
}

.btn-delete:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 批量操作样式 */
.batch-actions {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: #f8fafc;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.batch-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
  min-height: 32px;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: #d97706;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #b45309;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 分页控件样式 */
.account-pagination {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: var(--bg-secondary, #f8fafc);
  border-radius: 6px;
  border: 1px solid var(--border-color, #e5e7eb);
}

.pagination-info {
  font-size: 0.7rem;
  color: var(--text-secondary, #6b7280);
  text-align: center;
  margin-bottom: 6px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 6px;
}

.page-numbers {
  display: flex;
  gap: 2px;
}

.page-size-select {
  display: block;
  margin: 0 auto;
  padding: 4px 6px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.75rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
}

.page-size-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-icon {
  font-size: 1rem;
}

.btn-text {
  font-size: inherit;
}
</style>