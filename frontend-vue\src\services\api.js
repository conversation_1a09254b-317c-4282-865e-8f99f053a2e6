// ===== 全局配置和常量 =====
// API 基础地址配置 - 根据环境变量动态配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8888';
console.log('🔧 环境变量调试:', import.meta.env);
console.log('🔧 VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);

// 调试信息
console.log('🔧 API配置调试:');
console.log('  - 环境变量 VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);
console.log('  - 当前域名:', window.location.hostname);
console.log('  - 使用API地址:', API_BASE_URL);
const EMAIL_LIMIT = 500;

// 会话管理
let currentSessionId = null;

// ===== API 通信模块 =====
/**
 * 通用 API 请求函数
 * @param {string} endpoint - API 端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API 响应数据
 */
async function apiRequest(endpoint, options = {}) {
    // 确保URL拼接正确，避免双斜杠
    const baseUrl = API_BASE_URL.endsWith('/') ? API_BASE_URL.slice(0, -1) : API_BASE_URL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const url = `${baseUrl}${cleanEndpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include', // 包含Cookie以支持会话
    };

    // 如果有会话ID，添加到请求头
    if (currentSessionId) {
        defaultOptions.headers['X-Session-ID'] = currentSessionId;
    }

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, finalOptions);

        // 检查响应头中的会话ID
        const responseSessionId = response.headers.get('X-Session-ID');
        if (responseSessionId) {
            currentSessionId = responseSessionId;
            console.log('会话ID已更新:', currentSessionId);
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        return data;
    } catch (error) {
        console.error('API 请求失败:', error);
        throw error;
    }
}

/**
 * 获取会话信息
 * @returns {Promise<Object>} 会话信息
 */
export async function fetchSessionInfo() {
    return await apiRequest('/api/session');
}

/**
 * 获取所有账户
 * @returns {Promise<Array>} 账户列表
 */
export async function fetchAccounts() {
    return await apiRequest('/api/accounts');
}

/**
 * 获取单个账户的邮件
 * @param {string} email - 邮箱地址
 * @param {Array<string>} folders - 文件夹列表
 * @param {number} limit - 邮件数量限制
 * @returns {Promise<Object>} 邮件数据
 */
export async function fetchEmailsForAccount(email, folders, limit = EMAIL_LIMIT) {
    const foldersParam = folders.join(',');
    return await apiRequest(`/api/mail/${encodeURIComponent(email)}?folders=${foldersParam}&limit=${limit}`);
}

/**
 * 聚合获取多个账户的邮件 (同步版本)
 * @param {Array<string>} accounts - 账户列表
 * @param {Array<string>} folders - 文件夹列表
 * @param {number} limit - 每个文件夹的邮件数量限制
 * @returns {Promise<Object>} 聚合邮件数据
 */
export async function fetchAggregatedEmails(accounts, folders, limit = EMAIL_LIMIT) {
    return await apiRequest('/api/mail/aggregate', {
        method: 'POST',
        body: JSON.stringify({
            accounts: accounts,
            folders: folders,
            limit: limit
        })
    });
}

/**
 * 异步聚合获取多个账户的邮件 (并发优化版本)
 * @param {Array<string>} accounts - 账户列表
 * @param {Array<string>} folders - 文件夹列表
 * @param {number} limit - 每个文件夹的邮件数量限制
 * @returns {Promise<Object>} 聚合邮件数据
 */
export async function fetchAggregatedEmailsAsync(accounts, folders, limit = EMAIL_LIMIT) {
    return await apiRequest('/api/mail/aggregate-async', {
        method: 'POST',
        body: JSON.stringify({
            accounts: accounts,
            folders: folders,
            limit: limit
        })
    });
}

/**
 * 添加单个账户
 * @param {Object} accountData - 账户数据
 * @returns {Promise<Object>} 添加结果
 */
export async function addAccount(accountData) {
    return await apiRequest('/api/accounts', {
        method: 'POST',
        body: JSON.stringify(accountData)
    });
}

/**
 * 批量添加账户
 * @param {Array<Object>} accountsData - 账户数据列表
 * @returns {Promise<Object>} 批量添加结果
 */
export async function addAccountsBatch(accountsData) {
    return await apiRequest('/api/accounts/batch', {
        method: 'POST',
        body: JSON.stringify({
            accounts: accountsData
        })
    });
}

/**
 * 删除账户
 * @param {string} emailAddress - 邮箱地址
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteAccount(emailAddress) {
    return await apiRequest(`/api/accounts/${encodeURIComponent(emailAddress)}`, {
        method: 'DELETE'
    });
}
