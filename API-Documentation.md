# 多账户邮件聚合系统 API 文档

## 📋 目录
- [API概述](#api概述)
- [基础配置](#基础配置)
- [认证与会话](#认证与会话)
- [核心接口](#核心接口)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## 🚀 API概述

多账户邮件聚合系统是一个基于Flask的RESTful API服务，支持：

- **多账户管理** - 添加、查询、删除Gmail账户
- **邮件聚合获取** - 同步/异步获取多个账户的邮件
- **文件夹支持** - 支持收件箱、已发送、草稿等多个文件夹
- **会话隔离** - 每个用户会话的数据完全隔离
- **高性能** - 异步并发获取，支持大量账户

## ⚙️ 基础配置

### API基础信息
- **生产环境API地址**: `https://outlook.jx099.com`
- **前端界面**: `https://mail.jx099.com`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 请求头配置
```http
Content-Type: application/json
Accept: application/json
```

### 会话支持
API支持基于Cookie的会话管理，客户端需要：
- 发送请求时包含 `credentials: 'include'`
- 保持Cookie以维持会话状态

## 🔐 认证与会话

### 会话管理
系统使用会话ID进行数据隔离，每个会话的账户数据完全独立。

#### 获取会话信息
```bash
curl -X GET "https://outlook.jx099.com/api/session" \
  -H "Content-Type: application/json" \
  -c cookies.txt
```

**响应示例**:
```json
{
  "session_id": "abc123def456",
  "created_at": "2025-01-15T10:30:00Z",
  "status": "active"
}
```

## 📡 核心接口

### 1. 健康检查

#### GET /health
检查API服务状态和数据库连接。

```bash
curl -X GET "https://outlook.jx099.com/health"
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "mail-aggregator",
  "version": "1.0.0",
  "timestamp": "2025-01-15T10:30:00.000Z",
  "database": "connected"
}
```

### 2. 账户管理

#### GET /api/accounts
获取当前会话的所有账户列表。

```bash
curl -X GET "https://outlook.jx099.com/api/accounts" \
  -H "Content-Type: application/json" \
  -b cookies.txt
```

**响应示例**:
```json
{
  "accounts": [
    {
      "id": 1,
      "email_address": "<EMAIL>",
      "client_id": "your-client-id",
      "status": "OK",
      "last_updated": "2025-01-15T10:30:00.000Z"
    }
  ],
  "total_count": 1,
  "session_id": "abc123def456"
}
```

#### POST /api/accounts
添加新的Gmail账户。

```bash
curl -X POST "https://outlook.jx099.com/api/accounts" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-google-client-id",
    "refresh_token": "your-refresh-token"
  }'
```

**请求参数**:
- `email_address` (string, 必需): Gmail邮箱地址
- `client_id` (string, 必需): Google OAuth2 客户端ID
- `refresh_token` (string, 必需): Google OAuth2 刷新令牌

**响应示例**:
```json
{
  "message": "账户添加成功",
  "account": {
    "id": 2,
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "status": "OK",
    "last_updated": "2025-01-15T10:35:00.000Z"
  }
}
```

#### DELETE /api/accounts/{email}
删除指定的账户。

```bash
curl -X DELETE "https://outlook.jx099.com/api/accounts/<EMAIL>" \
  -H "Content-Type: application/json" \
  -b cookies.txt
```

**响应示例**:
```json
{
  "message": "账户删除成功",
  "email_address": "<EMAIL>"
}
```

### 3. 邮件获取

#### GET /api/mail/{email}
获取单个账户的邮件。

```bash
curl -X GET "https://outlook.jx099.com/api/mail/<EMAIL>?folders=inbox,sent&limit=10" \
  -H "Content-Type: application/json" \
  -b cookies.txt
```

**查询参数**:
- `folders` (string, 可选): 文件夹列表，逗号分隔，默认为 "inbox"
- `limit` (integer, 可选): 每个文件夹的邮件数量限制，默认为 5

**支持的文件夹**:
- `inbox` - 收件箱
- `sent` - 已发送
- `drafts` - 草稿
- `spam` - 垃圾邮件
- `trash` - 回收站

**响应示例**:
```json
{
  "email_address": "<EMAIL>",
  "folders": ["inbox", "sent"],
  "total_count": 15,
  "messages": [
    {
      "subject": "重要邮件",
      "sender": "<EMAIL>",
      "recipient": "<EMAIL>",
      "date": "2025-01-15T09:30:00Z",
      "body": "邮件内容...",
      "folder": "inbox"
    }
  ]
}
```

#### POST /api/mail/aggregate
同步聚合获取多个账户的邮件。

```bash
curl -X POST "https://outlook.jx099.com/api/mail/aggregate" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "accounts": ["<EMAIL>", "<EMAIL>"],
    "folders": ["inbox", "sent"],
    "limit": 10
  }'
```

**请求参数**:
- `accounts` (array, 必需): 邮箱地址列表
- `folders` (array, 可选): 文件夹列表，默认为 ["inbox"]
- `limit` (integer, 可选): 每个文件夹的邮件数量限制，默认为 5

#### POST /api/mail/aggregate-async
异步聚合获取多个账户的邮件（推荐用于大量账户）。

```bash
curl -X POST "https://outlook.jx099.com/api/mail/aggregate-async" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "accounts": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
    "folders": ["inbox"],
    "limit": 20
  }'
```

**响应示例**:
```json
{
  "total_messages": 45,
  "successful_accounts": ["<EMAIL>", "<EMAIL>"],
  "failed_accounts": ["<EMAIL>"],
  "duration": 3.24,
  "folders": ["inbox"],
  "messages": [
    {
      "subject": "邮件标题",
      "sender": "<EMAIL>",
      "recipient": "<EMAIL>",
      "date": "2025-01-15T09:30:00Z",
      "body": "邮件内容...",
      "folder": "inbox"
    }
  ]
}
```

## ❌ 错误处理

### 常见HTTP状态码

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必需字段 |
| 404 | 资源不存在 | 检查URL路径和资源ID |
| 409 | 资源冲突 | 账户已存在，使用不同的邮箱地址 |
| 500 | 服务器内部错误 | 稍后重试或联系技术支持 |
| 503 | 服务不可用 | 服务维护中，稍后重试 |

### 错误响应格式

```json
{
  "error": "ACCOUNT_NOT_FOUND",
  "message": "指定的账户不存在",
  "email_address": "<EMAIL>",
  "status_code": 404
}
```

### 常见错误类型

#### 1. 账户相关错误
```json
{
  "error": "ACCOUNT_EXISTS",
  "message": "账户已存在",
  "email_address": "<EMAIL>"
}
```

#### 2. 认证错误
```json
{
  "error": "GMAIL_AUTH_ERROR",
  "message": "Gmail认证失败，请检查refresh_token",
  "email_address": "<EMAIL>"
}
```

#### 3. 邮件获取错误
```json
{
  "error": "MAIL_FETCH_ERROR",
  "message": "邮件获取失败: IMAP连接超时",
  "email_address": "<EMAIL>"
}
```

## 💡 最佳实践

### 1. 会话管理
- 始终在请求中包含Cookie以维持会话
- 定期检查会话状态，必要时重新建立会话

### 2. 错误处理
- 实现重试机制，特别是网络相关错误
- 记录详细的错误日志便于调试
- 对用户友好的错误提示

### 3. 性能优化
- 大量账户使用异步接口 (`/api/mail/aggregate-async`)
- 合理设置邮件数量限制，避免超时
- 使用适当的文件夹过滤减少不必要的请求

### 4. 安全考虑
- 妥善保管Google OAuth2凭据
- 使用HTTPS进行所有API通信
- 定期更新refresh_token

### 5. 监控和日志
- 监控API响应时间和成功率
- 记录关键操作的审计日志
- 设置告警机制

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 前端界面: https://mail.jx099.com
- API服务: https://outlook.jx099.com
- 健康检查: https://outlook.jx099.com/health

## 🔧 高级用法示例

### 批量账户管理

#### 批量添加账户
```javascript
// JavaScript示例：批量添加多个账户
const accounts = [
  {
    email_address: "<EMAIL>",
    client_id: "your-client-id-1",
    refresh_token: "refresh-token-1"
  },
  {
    email_address: "<EMAIL>",
    client_id: "your-client-id-2",
    refresh_token: "refresh-token-2"
  }
];

for (const account of accounts) {
  try {
    const response = await fetch('https://outlook.jx099.com/api/accounts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(account)
    });

    if (response.ok) {
      console.log(`✅ 账户 ${account.email_address} 添加成功`);
    } else {
      console.error(`❌ 账户 ${account.email_address} 添加失败`);
    }
  } catch (error) {
    console.error(`网络错误: ${error.message}`);
  }
}
```

### 智能邮件聚合

#### 按优先级获取邮件
```bash
# 1. 首先获取重要文件夹的邮件
curl -X POST "https://outlook.jx099.com/api/mail/aggregate-async" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "accounts": ["<EMAIL>", "<EMAIL>"],
    "folders": ["inbox"],
    "limit": 50
  }'

# 2. 然后获取其他文件夹的邮件
curl -X POST "https://outlook.jx099.com/api/mail/aggregate-async" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "accounts": ["<EMAIL>", "<EMAIL>"],
    "folders": ["sent", "drafts"],
    "limit": 20
  }'
```

### 错误恢复策略

#### Python示例：带重试的邮件获取
```python
import requests
import time
from typing import List, Dict, Any

class MailAggregatorClient:
    def __init__(self, base_url: str = "https://outlook.jx099.com"):
        self.base_url = base_url
        self.session = requests.Session()

    def fetch_emails_with_retry(self, accounts: List[str],
                               folders: List[str] = None,
                               limit: int = 10,
                               max_retries: int = 3) -> Dict[str, Any]:
        """带重试机制的邮件获取"""
        folders = folders or ["inbox"]

        for attempt in range(max_retries):
            try:
                response = self.session.post(
                    f"{self.base_url}/api/mail/aggregate-async",
                    json={
                        "accounts": accounts,
                        "folders": folders,
                        "limit": limit
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 503:
                    # 服务暂时不可用，等待后重试
                    wait_time = 2 ** attempt  # 指数退避
                    print(f"服务暂时不可用，{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    response.raise_for_status()

            except requests.exceptions.Timeout:
                print(f"请求超时，尝试 {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                raise
            except requests.exceptions.RequestException as e:
                print(f"请求失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                raise

        raise Exception(f"重试 {max_retries} 次后仍然失败")

# 使用示例
client = MailAggregatorClient()
try:
    result = client.fetch_emails_with_retry(
        accounts=["<EMAIL>", "<EMAIL>"],
        folders=["inbox", "sent"],
        limit=25
    )
    print(f"成功获取 {result['total_messages']} 封邮件")
except Exception as e:
    print(f"获取邮件失败: {e}")
```

## 📊 性能基准

### 响应时间参考

| 操作 | 单账户 | 5个账户 | 10个账户 | 20个账户 |
|------|--------|---------|----------|----------|
| 同步聚合 | ~2s | ~8s | ~15s | ~30s |
| 异步聚合 | ~2s | ~3s | ~4s | ~6s |
| 账户查询 | ~100ms | ~100ms | ~100ms | ~100ms |
| 健康检查 | ~50ms | ~50ms | ~50ms | ~50ms |

### 并发限制
- **最大并发连接**: 50个
- **单会话最大账户数**: 100个
- **单次请求最大邮件数**: 500封
- **请求超时时间**: 60秒

## 🛡️ 安全最佳实践

### 1. OAuth2 令牌管理
```javascript
// 安全存储refresh_token的示例
class SecureTokenManager {
  constructor() {
    this.tokens = new Map();
  }

  // 加密存储token（实际应用中使用更强的加密）
  storeToken(email, refreshToken) {
    const encrypted = btoa(refreshToken); // 简单示例，实际应使用AES等
    this.tokens.set(email, encrypted);
  }

  // 获取并解密token
  getToken(email) {
    const encrypted = this.tokens.get(email);
    return encrypted ? atob(encrypted) : null;
  }

  // 清除token
  clearToken(email) {
    this.tokens.delete(email);
  }
}
```

### 2. 请求签名验证
```bash
# 使用时间戳防止重放攻击
TIMESTAMP=$(date +%s)
curl -X POST "https://outlook.jx099.com/api/accounts" \
  -H "Content-Type: application/json" \
  -H "X-Timestamp: $TIMESTAMP" \
  -b cookies.txt \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "refresh_token": "your-refresh-token"
  }'
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 会话丢失
**症状**: 返回空的账户列表或会话相关错误
**解决方案**:
```bash
# 重新建立会话
curl -X GET "https://outlook.jx099.com/api/session" \
  -c cookies.txt
```

#### 2. Gmail认证失败
**症状**: `GMAIL_AUTH_ERROR` 错误
**解决方案**:
1. 检查refresh_token是否有效
2. 确认Google OAuth2应用配置正确
3. 检查账户是否启用了IMAP访问

#### 3. 邮件获取超时
**症状**: 请求超时或部分账户失败
**解决方案**:
1. 减少单次请求的账户数量
2. 降低邮件数量限制
3. 使用异步接口替代同步接口

#### 4. 高并发限制
**症状**: 503 Service Unavailable
**解决方案**:
```javascript
// 实现请求队列和限流
class RateLimitedClient {
  constructor(maxConcurrent = 5) {
    this.maxConcurrent = maxConcurrent;
    this.currentRequests = 0;
    this.queue = [];
  }

  async request(url, options) {
    return new Promise((resolve, reject) => {
      this.queue.push({ url, options, resolve, reject });
      this.processQueue();
    });
  }

  async processQueue() {
    if (this.currentRequests >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    this.currentRequests++;
    const { url, options, resolve, reject } = this.queue.shift();

    try {
      const response = await fetch(url, options);
      resolve(response);
    } catch (error) {
      reject(error);
    } finally {
      this.currentRequests--;
      this.processQueue();
    }
  }
}
```

## 📈 监控和分析

### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh - API健康监控脚本

API_URL="https://outlook.jx099.com"
LOG_FILE="/var/log/mail-aggregator-health.log"

check_health() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$API_URL/health")

    if [ "$response" = "200" ]; then
        echo "[$timestamp] ✅ API健康检查通过" >> "$LOG_FILE"
        return 0
    else
        echo "[$timestamp] ❌ API健康检查失败 (HTTP $response)" >> "$LOG_FILE"
        # 发送告警通知
        send_alert "API健康检查失败"
        return 1
    fi
}

send_alert() {
    local message="$1"
    # 这里可以集成邮件、短信或其他告警系统
    echo "告警: $message"
}

# 执行检查
check_health
```

### 性能监控
```javascript
// 性能监控示例
class APIMonitor {
  constructor() {
    this.metrics = {
      requests: 0,
      errors: 0,
      totalTime: 0,
      avgResponseTime: 0
    };
  }

  async monitoredRequest(url, options) {
    const startTime = Date.now();
    this.metrics.requests++;

    try {
      const response = await fetch(url, options);

      if (!response.ok) {
        this.metrics.errors++;
      }

      const endTime = Date.now();
      const responseTime = endTime - startTime;
      this.metrics.totalTime += responseTime;
      this.metrics.avgResponseTime = this.metrics.totalTime / this.metrics.requests;

      console.log(`请求耗时: ${responseTime}ms, 平均响应时间: ${this.metrics.avgResponseTime.toFixed(2)}ms`);

      return response;
    } catch (error) {
      this.metrics.errors++;
      throw error;
    }
  }

  getStats() {
    return {
      ...this.metrics,
      errorRate: (this.metrics.errors / this.metrics.requests * 100).toFixed(2) + '%'
    };
  }
}
```

---
*文档版本: v1.0.0 | 最后更新: 2025-01-15*
