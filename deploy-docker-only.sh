#!/bin/bash

# 纯净 Docker 部署脚本 - 只部署容器，不包含反向代理和SSL配置
# 作者：邮件聚合系统

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 邮件聚合系统 - 纯净 Docker 部署${NC}"
echo "=================================================="

# 检查 Docker 环境
echo -e "${YELLOW}📋 检查 Docker 环境...${NC}"

if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查环境变量文件
ENV_FILE=".env.production"
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}⚠️  环境变量文件不存在，创建默认配置...${NC}"
    if [ -f ".env.production.template" ]; then
        cp .env.production.template $ENV_FILE
        # 自动设置一些默认值
        sed -i 's/your-super-secret-key-here-at-least-32-characters-long/8f591yB_e5VwLpYd6a3h3oT3nQ1wP8qL-X9gV7bKz0c/' $ENV_FILE
        sed -i 's/https:\/\/your-frontend-domain.netlify.app/https:\/\/您的前端域名.netlify.app/' $ENV_FILE
        echo -e "${GREEN}✅ 已创建默认环境变量文件${NC}"
        echo -e "${YELLOW}📝 如需修改，请编辑 $ENV_FILE 文件${NC}"
    else
        echo -e "${YELLOW}📝 创建基本环境变量文件...${NC}"
        cat > $ENV_FILE << EOF
# 生产环境变量
SECRET_KEY=8f591yB_e5VwLpYd6a3h3oT3nQ1wP8qL-X9gV7bKz0c
FRONTEND_URL=https://您的前端域名.netlify.app
FLASK_ENV=production
LOG_LEVEL=INFO
PORT=8888
EMAIL_LIMIT=500
RATELIMIT_ENABLED=true
EOF
        echo -e "${GREEN}✅ 已创建基本环境变量文件${NC}"
    fi
fi

# 加载环境变量
if [ -f "$ENV_FILE" ]; then
    export $(cat $ENV_FILE | grep -v '^#' | grep -v '^$' | xargs)
fi

# 验证必需的环境变量
echo -e "${YELLOW}🔍 验证环境变量...${NC}"

if [ -z "$SECRET_KEY" ]; then
    echo -e "${RED}❌ SECRET_KEY 未设置${NC}"
    exit 1
fi

if [ -z "$FRONTEND_URL" ]; then
    echo -e "${RED}❌ FRONTEND_URL 未设置${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境变量验证通过${NC}"

# 创建必要的目录
echo -e "${YELLOW}📁 创建数据目录...${NC}"
mkdir -p data logs backups
chmod 755 data logs backups

# 停止现有容器
echo -e "${YELLOW}🛑 停止现有容器...${NC}"
docker-compose -f docker-compose.simple.yml down || true

# 构建镜像
echo -e "${YELLOW}🏗️  构建Docker镜像...${NC}"
cd backend
docker build -t mail-aggregator .
cd ..

# 运行容器
echo -e "${YELLOW}🚀 启动Docker容器...${NC}"
docker run -d \
  --name mail-aggregator \
  -p 8888:8888 \
  -v $(pwd)/backend/data:/app/data \
  -v $(pwd)/backend/logs:/app/logs \
  -e FLASK_ENV=production \
  -e DATABASE_PATH=/app/data/accounts.db \
  -e LOG_FILE=/app/logs/app.log \
  -e SECRET_KEY=${SECRET_KEY} \
  -e FRONTEND_URL=${FRONTEND_URL} \
  -e LOG_LEVEL=${LOG_LEVEL:-INFO} \
  -e PORT=8888 \
  --restart unless-stopped \
  mail-aggregator

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"
PORT=${API_PORT:-8888}

if curl -f http://localhost:$PORT/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端服务健康检查通过${NC}"
else
    echo -e "${RED}❌ 后端服务健康检查失败${NC}"
    echo -e "${YELLOW}📋 查看日志：${NC}"
    docker-compose -f docker-compose.simple.yml logs mail-aggregator-api
    exit 1
fi

# 显示部署信息
echo ""
echo -e "${GREEN}🎉 Docker 部署完成！${NC}"
echo "=================================================="
echo -e "${BLUE}📊 服务信息：${NC}"
echo -e "   后端服务: http://localhost:$PORT"
echo -e "   健康检查: http://localhost:$PORT/health"
echo -e "   API 状态: http://localhost:$PORT/api/accounts"
echo ""
echo -e "${BLUE}🔧 管理命令：${NC}"
echo -e "   查看日志: docker-compose -f docker-compose.simple.yml logs -f"
echo -e "   停止服务: docker-compose -f docker-compose.simple.yml down"
echo -e "   重启服务: docker-compose -f docker-compose.simple.yml restart"
echo ""
echo -e "${YELLOW}📝 下一步：${NC}"
echo -e "   1. 测试 API: curl http://localhost:$PORT/health"
echo -e "   2. 配置反向代理（如需要）"
echo -e "   3. 设置 SSL 证书（如需要）"
echo ""
echo -e "${GREEN}✨ 纯净 Docker 部署完成！${NC}"
