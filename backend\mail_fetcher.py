"""
邮件获取模块
封装获取 Access Token 和收取邮件的核心逻辑
"""

import imaplib
import email
import requests
import logging
from typing import List, Dict, Any, Tuple, Union
from email.utils import parsedate_to_datetime
from email.header import decode_header, make_header
from datetime import datetime
from models import AccountDetail, MailMessage

# 配置日志
logger = logging.getLogger(__name__)


class MailFetcher:
    """邮件获取器类"""

    def __init__(self):
        self.imap_server = 'outlook.live.com'
        # 域名配置映射 - 不同域名使用不同的OAuth2端点
        self.domain_config = {
            'outlook.com': {
                'token_url': 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
                'description': 'Outlook.com accounts (common endpoint)'
            },
            'hotmail.com': {
                'token_url': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token',
                'description': 'Hotmail.com accounts (consumers endpoint)'
            },
            'live.com': {
                'token_url': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token',
                'description': 'Live.com accounts (consumers endpoint)'
            }
        }
        # 默认端点（向后兼容）
        self.default_token_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'

    def get_token_url_for_domain(self, email_address: str) -> str:
        """
        根据邮箱域名获取正确的OAuth2端点

        Args:
            email_address: 邮箱地址

        Returns:
            str: 对应的OAuth2 token端点URL
        """
        try:
            domain = email_address.split('@')[1].lower()
            config = self.domain_config.get(domain)

            if config:
                token_url = config['token_url']
                logger.info(f"域名 {domain} 使用端点: {token_url} ({config['description']})")
                return token_url
            else:
                logger.warning(f"未知域名 {domain}，使用默认端点: {self.default_token_url}")
                return self.default_token_url

        except Exception as e:
            logger.error(f"解析邮箱域名失败 {email_address}: {e}")
            return self.default_token_url

    def get_access_token(self, client_id: str, refresh_token: str, email_address: str = None) -> Tuple[bool, str]:
        """
        使用 refresh_token 获取新的 access_token

        Args:
            client_id: 微软应用的 Client ID
            refresh_token: OAuth2 Refresh Token
            email_address: 邮箱地址（用于确定正确的OAuth2端点）

        Returns:
            Tuple[bool, str]: (是否成功, access_token 或错误信息)
        """
        try:
            # 根据邮箱域名选择正确的token端点
            if email_address:
                token_url = self.get_token_url_for_domain(email_address)
            else:
                token_url = self.default_token_url
                logger.warning("未提供邮箱地址，使用默认OAuth2端点")

            data = {
                'client_id': client_id,
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token,
            }

            logger.info(f"正在向 {token_url} 请求access_token")
            response = requests.post(token_url, data=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            error = result.get('error')
            
            if error:
                error_description = result.get('error_description', error)
                logger.error(f"获取 access_token 失败: {error} - {error_description}")
                return False, f"Token 获取失败: {error_description}"
            
            access_token = result.get('access_token')
            if not access_token:
                logger.error("响应中没有 access_token")
                return False, "响应中没有 access_token"
            
            logger.info("成功获取 access_token")
            return True, access_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {e}")
            return False, f"网络请求失败: {str(e)}"
        except Exception as e:
            logger.error(f"获取 access_token 时发生未知错误: {e}")
            return False, f"未知错误: {str(e)}"
    
    def generate_auth_string(self, email_address: str, access_token: str) -> str:
        """
        生成 IMAP XOAUTH2 认证字符串
        
        Args:
            email_address: 邮箱地址
            access_token: Access Token
            
        Returns:
            str: 认证字符串
        """
        auth_string = f"user={email_address}\1auth=Bearer {access_token}\1\1"
        return auth_string
    
    def fetch_emails(self, account: AccountDetail, folders: List[str] = None, limit: int = 5) -> Union[List[Dict[str, Any]], Dict[str, str]]:
        """
        获取指定账户的邮件 (支持多文件夹)

        Args:
            account: 账户详细信息
            folders: 邮箱文件夹列表 (默认: ['inbox'])
            limit: 每个文件夹的邮件数量限制 (默认: 5)

        Returns:
            Union[List[Dict], Dict]: 邮件列表或错误信息
        """
        # 设置默认文件夹
        if folders is None:
            folders = ['inbox']

        # 首先获取 access_token（传入邮箱地址以选择正确的OAuth2端点）
        success, token_or_error = self.get_access_token(account.client_id, account.refresh_token, account.email_address)
        if not success:
            return {"error_key": "Token失效", "error_msg": token_or_error}

        access_token = token_or_error

        # 初始化存储所有邮件的列表
        all_messages = []
        failed_folders = []  # 记录失败的文件夹

        try:
            # 连接到 IMAP 服务器
            mail = imaplib.IMAP4_SSL(self.imap_server)

            # 使用 XOAUTH2 认证
            auth_string = self.generate_auth_string(account.email_address, access_token)
            mail.authenticate('XOAUTH2', lambda x: auth_string)

            # 文件夹映射
            folder_map = {
                'inbox': 'inbox',
                'junk': 'Junk',
                'sent': 'Sent Items',
                'drafts': 'Drafts',
                'deleted': 'Deleted Items'
            }

            # 遍历每个文件夹
            for folder_name in folders:
                try:
                    logger.info(f"正在处理文件夹: {folder_name}")

                    # 获取实际的文件夹名称
                    actual_folder = folder_map.get(folder_name.lower(), folder_name)
                    result, _ = mail.select(actual_folder)

                    if result != 'OK':
                        logger.warning(f"无法访问文件夹: {folder_name}")
                        failed_folders.append(folder_name)
                        continue

                    # 搜索当前文件夹中的所有邮件
                    result, data = mail.search(None, 'ALL')
                    if result != 'OK':
                        logger.warning(f"搜索文件夹 {folder_name} 失败")
                        failed_folders.append(folder_name)
                        continue

                    # 获取邮件ID列表，按时间倒序排列
                    mail_ids = sorted(data[0].split(), reverse=True)

                    # 限制获取数量
                    limited_mail_ids = mail_ids[:limit]

                    # 处理当前文件夹的邮件
                    folder_messages = []

                    for mail_id in limited_mail_ids:
                        try:
                            # 获取邮件内容
                            result, msg_data = mail.fetch(mail_id, "(RFC822)")

                            if result != 'OK':
                                logger.warning(f"获取邮件 {mail_id} 失败")
                                continue

                            # 解析邮件
                            raw_email = msg_data[0][1]
                            email_message = email.message_from_bytes(raw_email)

                            # 提取邮件信息
                            mail_info = self._parse_email_message(email_message)
                            if mail_info:
                                # 关键：为每封邮件添加 folder 字段
                                mail_info['folder'] = folder_name
                                folder_messages.append(mail_info)

                        except Exception as e:
                            logger.error(f"解析邮件 {mail_id} 时出错: {e}")
                            continue

                    # 将当前文件夹的邮件添加到总列表中
                    all_messages.extend(folder_messages)
                    logger.info(f"从文件夹 {folder_name} 获取了 {len(folder_messages)} 封邮件")

                except Exception as e:
                    logger.error(f"处理文件夹 {folder_name} 时出错: {e}")
                    failed_folders.append(folder_name)
                    continue

            # 关闭 IMAP 连接
            mail.logout()

            # 按邮件日期进行全局倒序排序
            def parse_date_for_sorting(mail_info):
                """解析邮件日期用于排序"""
                try:
                    date_str = mail_info.get('date', '')
                    if date_str and date_str != "未知时间" and date_str != "日期解析失败":
                        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                    else:
                        return datetime.min  # 无效日期排在最后
                except:
                    return datetime.min

            # 对所有邮件按日期倒序排序
            all_messages.sort(key=parse_date_for_sorting, reverse=True)

            # 记录结果
            total_messages = len(all_messages)
            successful_folders = [f for f in folders if f not in failed_folders]

            logger.info(f"成功获取 {total_messages} 封邮件，来自 {len(successful_folders)} 个文件夹")
            if failed_folders:
                logger.warning(f"以下文件夹处理失败: {failed_folders}")

            return all_messages
            
        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP 错误: {e}")
            return {"error_key": "IMAP错误", "error_msg": f"IMAP 连接或操作失败: {str(e)}"}
        except Exception as e:
            logger.error(f"获取邮件时发生未知错误: {e}")
            return {"error_key": "未知错误", "error_msg": f"获取邮件失败: {str(e)}"}
    
    def _parse_email_message(self, email_message) -> Dict[str, str]:
        """
        解析邮件消息对象
        
        Args:
            email_message: email.message.EmailMessage 对象
            
        Returns:
            Dict[str, str]: 解析后的邮件信息
        """
        try:
            # 解析邮件头信息
            subject = str(make_header(decode_header(email_message['SUBJECT']))) if email_message['SUBJECT'] else "无主题"
            mail_from = str(make_header(decode_header(email_message['From']))).replace('<', '(').replace('>', ')') if email_message['From'] else "未知发件人"
            mail_to = str(make_header(decode_header(email_message['To']))).replace('<', '(').replace('>', ')') if email_message['To'] else "未知收件人"
            
            # 解析日期
            try:
                mail_dt = parsedate_to_datetime(email_message['Date']).strftime("%Y-%m-%d %H:%M:%S") if email_message['Date'] else "未知时间"
            except:
                mail_dt = "日期解析失败"
            
            # 解析邮件正文
            body = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    if content_type in ["text/plain", "text/html"]:
                        try:
                            payload = part.get_payload(decode=True)
                            if payload:
                                # 尝试获取正确的字符编码
                                charset = part.get_content_charset() or 'utf-8'
                                try:
                                    decoded_body = payload.decode(charset)
                                except (UnicodeDecodeError, LookupError):
                                    # 如果指定编码失败，尝试常见编码
                                    for encoding in ['utf-8', 'gbk', 'gb2312', 'big5', 'iso-8859-1']:
                                        try:
                                            decoded_body = payload.decode(encoding)
                                            break
                                        except (UnicodeDecodeError, LookupError):
                                            continue
                                    else:
                                        # 所有编码都失败，使用utf-8并替换错误字符
                                        decoded_body = payload.decode('utf-8', errors='replace')
                                body += decoded_body
                        except Exception as e:
                            logger.warning(f"解析邮件部分时出错: {e}")
                            continue
            else:
                try:
                    payload = email_message.get_payload(decode=True)
                    if payload:
                        # 尝试获取正确的字符编码
                        charset = email_message.get_content_charset() or 'utf-8'
                        try:
                            body = payload.decode(charset)
                        except (UnicodeDecodeError, LookupError):
                            # 如果指定编码失败，尝试常见编码
                            for encoding in ['utf-8', 'gbk', 'gb2312', 'big5', 'iso-8859-1']:
                                try:
                                    body = payload.decode(encoding)
                                    break
                                except (UnicodeDecodeError, LookupError):
                                    continue
                            else:
                                # 所有编码都失败，使用utf-8并替换错误字符
                                body = payload.decode('utf-8', errors='replace')
                except Exception as e:
                    logger.error(f"解析邮件正文时出错: {e}")
                    body = "正文解析失败"
            
            return {
                "subject": subject,
                "sender": mail_from,
                "recipient": mail_to,
                "date": mail_dt,
                "body": body
            }
            
        except Exception as e:
            logger.error(f"解析邮件消息时出错: {e}")
            return {
                "subject": "解析失败",
                "sender": "解析失败",
                "recipient": "解析失败",
                "date": "解析失败",
                "body": f"邮件解析失败: {str(e)}"
            }


# 创建全局实例
mail_fetcher = MailFetcher()


def get_emails_for_account(account: AccountDetail, folders: List[str] = None, limit: int = 5) -> Union[List[Dict[str, Any]], Dict[str, str]]:
    """
    为指定账户获取邮件的便捷函数 (支持多文件夹)

    Args:
        account: 账户详细信息
        folders: 邮箱文件夹列表 (默认: ['inbox'])
        limit: 每个文件夹的邮件数量限制

    Returns:
        Union[List[Dict], Dict]: 邮件列表或错误信息
    """
    return mail_fetcher.fetch_emails(account, folders, limit)


# 保持向后兼容的单文件夹函数
def get_emails_for_single_folder(account: AccountDetail, folder: str = 'inbox', limit: int = 5) -> Union[List[Dict[str, Any]], Dict[str, str]]:
    """
    为指定账户获取单个文件夹邮件的便捷函数 (向后兼容)

    Args:
        account: 账户详细信息
        folder: 邮箱文件夹
        limit: 获取邮件数量限制

    Returns:
        Union[List[Dict], Dict]: 邮件列表或错误信息
    """
    return mail_fetcher.fetch_emails(account, [folder], limit)
