import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  return {
    plugins: [vue()],
    server: {
      port: 5173,
      host: true, // Allow external connections
      cors: true
    },
    build: {
      // Production build optimizations
      minify: 'esbuild', // 使用内置的 esbuild 压缩器
      // esbuild 压缩选项
      target: 'es2015',
      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            vendor: ['vue']
          }
        }
      },
      // Generate source maps for production debugging
      sourcemap: mode === 'production' ? 'hidden' : true,
      // Optimize chunk size
      chunkSizeWarningLimit: 1000
    },
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: mode !== 'production',
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    },
    // Ensure environment variables are properly loaded
    envPrefix: 'VITE_'
  }
})
