# 📧 多账户邮件聚合管理系统

一个基于Flask + Vue.js的现代化邮件聚合系统，支持多Gmail账户的统一管理和邮件获取。

## 🌟 系统特性

### 核心功能
- ✅ **多账户管理** - 支持添加、删除、查询多个Gmail账户
- ✅ **邮件聚合** - 同步/异步获取多个账户的邮件
- ✅ **文件夹支持** - 收件箱、已发送、草稿、垃圾邮件、回收站
- ✅ **会话隔离** - 每个用户会话的数据完全独立
- ✅ **高性能** - 异步并发处理，支持大量账户
- ✅ **现代界面** - 响应式Vue.js前端，支持移动端

### 技术特点
- 🚀 **异步处理** - 并发获取邮件，显著提升性能
- 🔒 **安全可靠** - OAuth2认证，HTTPS加密传输
- 📱 **跨平台** - Web界面，支持所有现代浏览器
- 🐳 **容器化** - Docker部署，易于扩展和维护
- 📊 **实时监控** - 健康检查和性能监控

## 🌐 在线访问

### 生产环境
- **前端界面**: https://mail.jx099.com
- **API服务**: https://outlook.jx099.com
- **健康检查**: https://outlook.jx099.com/health

### 系统状态
```bash
# 检查API服务状态
curl https://outlook.jx099.com/health
```

## 📚 文档导航

### 🚀 快速开始
- **[快速开始指南](Quick-Start-Guide.md)** - 5分钟上手使用
- 包含基本使用方法和代码示例
- Google OAuth2配置指南

### 📖 完整文档
- **[API完整文档](API-Documentation.md)** - 详细的API接口说明
- 包含所有端点、参数、响应格式
- 高级用法和最佳实践

## 🏗️ 系统架构

```
┌─────────────────┐    HTTPS     ┌─────────────────┐
│   前端界面      │ ──────────► │   后端API       │
│ mail.jx099.com  │             │outlook.jx099.com│
│                 │             │                 │
│ Vue.js + Vite   │             │ Flask + SQLite  │
│ Netlify部署     │             │ Docker部署      │
└─────────────────┘             └─────────────────┘
                                         │
                                         ▼
                                ┌─────────────────┐
                                │   Gmail API     │
                                │   OAuth2认证    │
                                │   IMAP协议      │
                                └─────────────────┘
```

## 🛠️ 技术栈

### 后端 (Flask API)
- **框架**: Flask 2.3.3
- **数据库**: SQLite (会话隔离)
- **认证**: Google OAuth2 + Gmail API
- **邮件协议**: IMAP
- **部署**: Docker + 云服务器

### 前端 (Vue.js SPA)
- **框架**: Vue.js 3.4.0
- **构建工具**: Vite 4.5.0
- **样式**: 原生CSS + 响应式设计
- **部署**: Netlify

### 开发工具
- **容器化**: Docker
- **版本控制**: Git
- **CI/CD**: Netlify自动部署

## 🚀 快速体验

### 方法1: 在线使用（推荐）
1. 访问 https://mail.jx099.com
2. 点击"导入邮箱"添加Gmail账户
3. 配置Google OAuth2凭据
4. 开始聚合邮件

### 方法2: API调用
```bash
# 1. 添加账户
curl -X POST "https://outlook.jx099.com/api/accounts" \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-google-client-id", 
    "refresh_token": "your-refresh-token"
  }'

# 2. 获取邮件
curl -X POST "https://outlook.jx099.com/api/mail/aggregate-async" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "accounts": ["<EMAIL>"],
    "folders": ["inbox"],
    "limit": 20
  }'
```

## 📊 性能表现

### 响应时间基准
| 操作类型 | 单账户 | 5个账户 | 10个账户 | 20个账户 |
|----------|--------|---------|----------|----------|
| 异步聚合 | ~2秒   | ~3秒    | ~4秒     | ~6秒     |
| 同步聚合 | ~2秒   | ~8秒    | ~15秒    | ~30秒    |
| 账户管理 | ~100毫秒| ~100毫秒| ~100毫秒 | ~100毫秒 |

### 系统限制
- **最大并发**: 50个连接
- **单会话账户数**: 100个
- **单次邮件数**: 500封
- **请求超时**: 60秒

## 🔧 本地开发

### 环境要求
- Python 3.11+
- Node.js 20+
- Docker (可选)

### 后端开发
```bash
# 克隆项目
git clone <repository-url>
cd mail-aggregator/backend

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python flask_main.py
```

### 前端开发
```bash
# 进入前端目录
cd frontend-vue

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### Docker部署
```bash
# 后端部署
cd backend
./deploy-simple.sh

# 检查状态
docker ps
docker logs mail-aggregator
```

## 🔐 安全说明

### OAuth2配置
1. 在Google Cloud Console创建项目
2. 启用Gmail API
3. 创建OAuth2客户端凭据
4. 获取refresh_token用于API调用

### 数据安全
- ✅ 所有通信使用HTTPS加密
- ✅ OAuth2标准认证流程
- ✅ 会话数据完全隔离
- ✅ 不存储邮件内容，仅实时获取
- ✅ 敏感信息环境变量管理

## 📈 监控和维护

### 健康检查
```bash
# API健康状态
curl https://outlook.jx099.com/health

# 预期响应
{
  "status": "healthy",
  "service": "mail-aggregator", 
  "version": "1.0.0",
  "database": "connected"
}
```

### 日志监控
```bash
# 查看应用日志
docker logs mail-aggregator -f

# 查看错误日志
docker logs mail-aggregator --tail=100 | grep ERROR
```

## 🤝 贡献指南

### 报告问题
- 使用GitHub Issues报告bug
- 提供详细的错误信息和复现步骤
- 包含系统环境信息

### 功能建议
- 在Issues中提出新功能建议
- 描述使用场景和预期效果
- 欢迎提交Pull Request

### 开发规范
- 遵循PEP 8 (Python) 和ESLint (JavaScript)
- 添加适当的注释和文档
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持

### 在线资源
- **系统界面**: https://mail.jx099.com
- **API服务**: https://outlook.jx099.com
- **文档**: 查看项目中的Markdown文件

### 联系方式
- **问题反馈**: 通过GitHub Issues
- **功能建议**: 通过GitHub Discussions
- **紧急问题**: 查看健康检查端点状态

---

## 🎯 路线图

### v1.1 (计划中)
- [ ] 邮件搜索和过滤功能
- [ ] 邮件标签和分类
- [ ] 导出功能 (CSV/JSON)
- [ ] 更多邮件服务商支持

### v1.2 (计划中)
- [ ] 邮件发送功能
- [ ] 定时任务和自动同步
- [ ] 高级统计和分析
- [ ] 移动端原生应用

---

*项目版本: v1.0.0 | 最后更新: 2025-01-15*

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
