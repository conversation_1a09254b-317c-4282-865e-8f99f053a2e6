# 纯净 Docker 部署环境变量模板
# 复制此文件为 .env.production 并填入实际值

# =============================================================================
# 必需变量 - 生产环境必须设置
# =============================================================================

# Flask 会话密钥 (生成命令: python3 -c "import secrets; print(secrets.token_urlsafe(32))")
SECRET_KEY=8f591yB_e5VwLpYd6a3h3oT3nQ1wP8qL-X9gV7bKz0c


# 前端部署地址
FRONTEND_URL=https://mail.jx099.com

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Port for the Flask application (default: 8888)
PORT=8888

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database file path (default: /app/data/accounts.db)
DATABASE_PATH=/app/data/accounts.db

# Database backup directory (default: /app/data/backups)
DATABASE_BACKUP_PATH=/app/data/backups

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# Additional CORS origins (comma-separated)
# Example: https://app.example.com,https://admin.example.com
ADDITIONAL_CORS_ORIGINS=

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log file path (default: /app/logs/app.log)
LOG_FILE=/app/logs/app.log

# Maximum log file size in bytes (default: 10MB)
LOG_MAX_BYTES=********

# Number of backup log files to keep (default: 5)
LOG_BACKUP_COUNT=5

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Maximum number of emails to fetch per account (default: 500)
EMAIL_LIMIT=500

# Email fetch timeout in seconds (default: 30)
EMAIL_FETCH_TIMEOUT=30

# =============================================================================
# SECURITY & PERFORMANCE
# =============================================================================

# Maximum request content length in bytes (default: 16MB)
MAX_CONTENT_LENGTH=********

# Enable rate limiting (true/false)
RATELIMIT_ENABLED=true

# Default rate limit (e.g., "100 per hour", "10 per minute")
RATELIMIT_DEFAULT=100 per hour

# Rate limit storage URL (default: memory://)
# For Redis: redis://localhost:6379
RATELIMIT_STORAGE_URL=memory://

# =============================================================================
# DEPLOYMENT SPECIFIC
# =============================================================================

# Environment name
FLASK_ENV=production

# Deployment platform (docker, heroku, aws, etc.)
DEPLOYMENT_PLATFORM=docker

# Health check endpoint enabled
HEALTH_CHECK_ENABLED=true
