{"name": "frontend-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:prod": "vite preview --mode production", "analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'"}, "dependencies": {"vue": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^4.5.0"}}