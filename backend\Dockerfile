# 简单的 Python Flask 应用 Dockerfile
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/data /app/logs

# 设置环境变量
ENV FLASK_ENV=production
ENV DATABASE_PATH=/app/data/accounts.db
ENV LOG_FILE=/app/logs/app.log
ENV PORT=8888
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8888

# 启动应用
CMD ["python", "flask_main.py"]
