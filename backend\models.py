"""
Pydantic 模型定义
用于 API 请求/响应体的数据校验和序列化
"""

from pydantic import BaseModel
from typing import Optional, List


class AccountCreate(BaseModel):
    """创建账户时的请求体模型"""
    email_address: str
    client_id: str
    refresh_token: str


class AccountResponse(BaseModel):
    """账户信息响应模型（不包含敏感信息）"""
    id: int
    email_address: str
    client_id: str
    status: str
    last_updated: str


class AccountDetail(BaseModel):
    """账户详细信息模型（包含所有字段）"""
    id: int
    email_address: str
    client_id: str
    refresh_token: str
    status: str
    last_updated: str


class MailMessage(BaseModel):
    """邮件消息模型"""
    subject: str
    sender: str
    recipient: str
    date: str
    body: str
    folder: str  # 新增：标明邮件来源文件夹


class AggregateMailRequest(BaseModel):
    """聚合邮件请求模型"""
    email_address: str
    folders: List[str]  # 要获取的文件夹列表
    limit: Optional[int] = 5  # 每个文件夹的邮件数量限制


class MailResponse(BaseModel):
    """邮件获取响应模型"""
    email_address: str
    folders: List[str]  # 修改：支持多个文件夹
    total_count: int
    messages: List[MailMessage]


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str
    message: str
    email_address: Optional[str] = None


class SuccessResponse(BaseModel):
    """成功响应模型"""
    message: str
    data: Optional[dict] = None
