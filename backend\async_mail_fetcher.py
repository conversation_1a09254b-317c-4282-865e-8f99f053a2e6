"""
异步邮件获取器模块
优化批量邮件获取的性能
"""

import asyncio
import concurrent.futures
import logging
from datetime import datetime
from typing import List, Dict, Any, Union
from models import AccountDetail
from mail_fetcher import mail_fetcher

# 设置日志
logger = logging.getLogger(__name__)

class AsyncMailFetcher:
    """异步邮件获取器类"""
    
    def __init__(self, max_workers: int = 5):
        """
        初始化异步邮件获取器
        
        Args:
            max_workers: 最大并发工作线程数
        """
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
    
    async def fetch_emails_for_account_async(self, account: AccountDetail, folders: List[str] = None, limit: int = 5) -> Union[List[Dict[str, Any]], Dict[str, str]]:
        """
        异步获取单个账户的邮件 - 直接使用原版mail_fetcher确保认证逻辑一致
        
        Args:
            account: 账户详细信息
            folders: 邮箱文件夹列表 (默认: ['inbox'])
            limit: 每个文件夹的邮件数量限制
            
        Returns:
            Union[List[Dict], Dict]: 邮件列表或错误信息
        """
        def _fetch_emails():
            # 直接使用原版的mail_fetcher，确保认证逻辑完全一致
            result = mail_fetcher.fetch_emails(account, folders, limit)
            
            # 为每封邮件添加账户信息
            if isinstance(result, list):
                for message in result:
                    message["account"] = account.email_address
            
            return result
        
        # 在线程池中执行邮件获取
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _fetch_emails)
    
    async def fetch_emails_batch_async(self, accounts: List[AccountDetail], folders: List[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        异步批量获取多个账户的邮件
        
        Args:
            accounts: 账户列表
            folders: 邮箱文件夹列表
            limit: 每个文件夹的邮件数量限制
            
        Returns:
            Dict: 包含所有邮件和统计信息的结果
        """
        logger.info(f"开始异步批量获取 {len(accounts)} 个账户的邮件")
        start_time = datetime.now()
        
        # 创建异步任务列表
        tasks = []
        for account in accounts:
            task = self.fetch_emails_for_account_async(account, folders, limit)
            tasks.append((account.email_address, task))
        
        # 并发执行所有任务
        results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        # 处理结果
        all_messages = []
        successful_accounts = []
        failed_accounts = []
        
        for i, (email_address, result) in enumerate(zip([email for email, _ in tasks], results)):
            if isinstance(result, Exception):
                # 任务执行异常
                logger.error(f"账户 {email_address} 处理异常: {result}")
                failed_accounts.append({
                    "email": email_address,
                    "error": f"处理异常: {str(result)}"
                })
            elif isinstance(result, dict) and "error_key" in result:
                # 邮件获取失败
                error_msg = result["error_msg"]
                logger.error(f"账户 {email_address} 邮件获取失败: {error_msg}")
                failed_accounts.append({
                    "email": email_address,
                    "error": error_msg
                })
            elif isinstance(result, list):
                # 成功获取邮件
                messages = result
                all_messages.extend(messages)
                successful_accounts.append(email_address)
                logger.info(f"账户 {email_address} 成功获取 {len(messages)} 封邮件")
            else:
                # 未知结果类型
                logger.error(f"账户 {email_address} 返回了未知的结果类型: {type(result)}")
                failed_accounts.append({
                    "email": email_address,
                    "error": f"未知结果类型: {type(result)}"
                })
        
        # 按邮件日期进行全局倒序排序
        def parse_date_for_sorting(mail_info):
            try:
                date_str = mail_info.get('date', '')
                if date_str and date_str != "未知时间" and date_str != "日期解析失败":
                    return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                else:
                    return datetime.min
            except:
                return datetime.min
        
        all_messages.sort(key=parse_date_for_sorting, reverse=True)
        
        # 计算耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"异步批量获取完成: 成功 {len(successful_accounts)} 个账户, 失败 {len(failed_accounts)} 个账户, 总邮件 {len(all_messages)} 封, 耗时 {duration:.2f} 秒")
        
        return {
            "messages": all_messages,
            "total_count": len(all_messages),
            "successful_accounts": successful_accounts,
            "failed_accounts": failed_accounts,
            "duration": duration,
            "folders": folders or ['inbox']
        }
    

    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)

# 创建全局异步实例
async_mail_fetcher = AsyncMailFetcher(max_workers=5)