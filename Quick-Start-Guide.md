# 🚀 多账户邮件聚合系统 - 快速开始指南

## 📋 5分钟快速上手

### 1. 访问系统
- **前端界面**: https://mail.jx099.com
- **API服务**: https://outlook.jx099.com

### 2. 检查服务状态
```bash
curl https://outlook.jx099.com/health
```

### 3. 添加Gmail账户
```bash
curl -X POST "https://outlook.jx099.com/api/accounts" \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-google-client-id",
    "refresh_token": "your-refresh-token"
  }'
```

### 4. 获取邮件
```bash
# 获取收件箱邮件
curl -X GET "https://outlook.jx099.com/api/mail/<EMAIL>?folders=inbox&limit=10" \
  -H "Content-Type: application/json" \
  -b cookies.txt
```

### 5. 多账户聚合（推荐）
```bash
curl -X POST "https://outlook.jx099.com/api/mail/aggregate-async" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "accounts": ["<EMAIL>", "<EMAIL>"],
    "folders": ["inbox"],
    "limit": 20
  }'
```

## 🔑 获取Google OAuth2凭据

### 步骤1: 创建Google Cloud项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用Gmail API

### 步骤2: 创建OAuth2凭据
1. 转到"凭据"页面
2. 点击"创建凭据" → "OAuth 2.0 客户端ID"
3. 选择"桌面应用程序"
4. 记录 `client_id` 和 `client_secret`

### 步骤3: 获取refresh_token
```python
# Python示例：获取refresh_token
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow

SCOPES = ['https://www.googleapis.com/auth/gmail.readonly']

def get_refresh_token():
    flow = InstalledAppFlow.from_client_secrets_file(
        'credentials.json', SCOPES)
    creds = flow.run_local_server(port=0)
    
    print(f"Refresh Token: {creds.refresh_token}")
    return creds.refresh_token
```

## 📱 支持的文件夹

| 文件夹名 | 说明 | 英文名称 |
|----------|------|----------|
| inbox | 收件箱 | INBOX |
| sent | 已发送 | [Gmail]/Sent Mail |
| drafts | 草稿 | [Gmail]/Drafts |
| spam | 垃圾邮件 | [Gmail]/Spam |
| trash | 回收站 | [Gmail]/Trash |

## ⚡ 性能建议

### 账户数量建议
- **1-5个账户**: 使用同步接口 `/api/mail/aggregate`
- **5-20个账户**: 使用异步接口 `/api/mail/aggregate-async`
- **20+个账户**: 分批处理，每批10-15个账户

### 邮件数量建议
- **快速预览**: limit=5-10
- **常规使用**: limit=20-50
- **深度分析**: limit=100-200

## 🛠️ 常用代码片段

### JavaScript/Node.js
```javascript
// 邮件聚合客户端
class MailAggregator {
  constructor(baseUrl = 'https://outlook.jx099.com') {
    this.baseUrl = baseUrl;
  }
  
  async addAccount(email, clientId, refreshToken) {
    const response = await fetch(`${this.baseUrl}/api/accounts`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({
        email_address: email,
        client_id: clientId,
        refresh_token: refreshToken
      })
    });
    return response.json();
  }
  
  async getEmails(accounts, folders = ['inbox'], limit = 20) {
    const response = await fetch(`${this.baseUrl}/api/mail/aggregate-async`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ accounts, folders, limit })
    });
    return response.json();
  }
}

// 使用示例
const client = new MailAggregator();
const result = await client.getEmails(['<EMAIL>'], ['inbox'], 10);
console.log(`获取到 ${result.total_messages} 封邮件`);
```

### Python
```python
import requests

class MailAggregatorClient:
    def __init__(self, base_url="https://outlook.jx099.com"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def add_account(self, email, client_id, refresh_token):
        response = self.session.post(
            f"{self.base_url}/api/accounts",
            json={
                "email_address": email,
                "client_id": client_id,
                "refresh_token": refresh_token
            }
        )
        return response.json()
    
    def get_emails(self, accounts, folders=None, limit=20):
        folders = folders or ["inbox"]
        response = self.session.post(
            f"{self.base_url}/api/mail/aggregate-async",
            json={
                "accounts": accounts,
                "folders": folders,
                "limit": limit
            }
        )
        return response.json()

# 使用示例
client = MailAggregatorClient()
result = client.get_emails(['<EMAIL>'], ['inbox'], 10)
print(f"获取到 {result['total_messages']} 封邮件")
```

### cURL脚本
```bash
#!/bin/bash
# mail_aggregator.sh - 邮件聚合脚本

API_URL="https://outlook.jx099.com"
COOKIE_FILE="cookies.txt"

# 添加账户
add_account() {
    local email="$1"
    local client_id="$2"
    local refresh_token="$3"
    
    curl -X POST "$API_URL/api/accounts" \
        -H "Content-Type: application/json" \
        -c "$COOKIE_FILE" \
        -d "{
            \"email_address\": \"$email\",
            \"client_id\": \"$client_id\",
            \"refresh_token\": \"$refresh_token\"
        }"
}

# 获取邮件
get_emails() {
    local accounts="$1"  # JSON数组格式: ["email1","email2"]
    local limit="${2:-20}"
    
    curl -X POST "$API_URL/api/mail/aggregate-async" \
        -H "Content-Type: application/json" \
        -b "$COOKIE_FILE" \
        -d "{
            \"accounts\": $accounts,
            \"folders\": [\"inbox\"],
            \"limit\": $limit
        }"
}

# 使用示例
# add_account "<EMAIL>" "your-client-id" "your-refresh-token"
# get_emails '["<EMAIL>"]' 10
```

## 🔍 故障排除

### 常见错误及解决方案

#### 1. 账户添加失败
```bash
# 检查账户是否已存在
curl -X GET "https://outlook.jx099.com/api/accounts" -b cookies.txt
```

#### 2. 邮件获取失败
```bash
# 检查账户状态
curl -X GET "https://outlook.jx099.com/api/accounts" -b cookies.txt | jq '.accounts[] | {email: .email_address, status: .status}'
```

#### 3. 会话问题
```bash
# 重新建立会话
curl -X GET "https://outlook.jx099.com/api/session" -c cookies.txt
```

## 📞 获取帮助

- **完整API文档**: 查看 `API-Documentation.md`
- **前端界面**: https://mail.jx099.com
- **健康检查**: https://outlook.jx099.com/health

---
*快速开始指南 v1.0.0 | 最后更新: 2025-01-15*
