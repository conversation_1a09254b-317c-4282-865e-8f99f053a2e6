<template>
  <aside class="email-detail-panel">
    <div class="detail-header">
      <h2 class="section-title">📄 邮件详情</h2>
    </div>
    
    <div class="detail-content">
      <!-- 默认提示 -->
      <div v-if="!selectedEmail" class="detail-empty">
        <div class="detail-empty-icon">👈</div>
        <div class="detail-empty-title">请选择邮件</div>
        <div class="detail-empty-description">在邮件列表中点击一封邮件查看详情</div>
      </div>
      
      <!-- 邮件详情内容容器 -->
      <div v-else class="detail-mail">
        <!-- 邮件头信息 -->
        <div class="mail-header">
          <div class="mail-subject">{{ selectedEmail.subject }}</div>
          <div class="mail-meta">
            <div class="mail-meta-item">
              <span class="meta-label">发件人:</span>
              <span class="meta-value">{{ selectedEmail.sender }}</span>
            </div>
            <div class="mail-meta-item">
              <span class="meta-label">收件人:</span>
              <span class="meta-value">{{ selectedEmail.recipient }}</span>
            </div>
            <div class="mail-meta-item">
              <span class="meta-label">时间:</span>
              <span class="meta-value">{{ formatDate(selectedEmail.date) }}</span>
            </div>
            <div class="mail-meta-item">
              <span class="meta-label">来源:</span>
              <span class="meta-value">
                <span class="badge badge-account">{{ getEmailFromSender(selectedEmail.recipient) }}</span>
                <span class="badge badge-folder">📁 {{ selectedEmail.folder }}</span>
              </span>
            </div>
          </div>
        </div>
        
        <!-- 邮件正文 -->
        <div class="mail-body">
          <div class="mail-content" v-html="sanitizeEmailContent(selectedEmail.body)"></div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup>
const props = defineProps({
  selectedEmail: {
    type: Object,
    default: null
  }
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return dateString
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

// 从发件人字符串中提取邮箱地址
const getEmailFromSender = (sender) => {
  if (!sender) return ''
  
  const emailMatch = sender.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
  return emailMatch ? emailMatch[1] : sender
}

// 安全地渲染邮件内容
const sanitizeEmailContent = (content) => {
  if (!content) return '<p>无内容</p>'
  
  // 处理可能的编码问题
  let processedContent = content
  
  // 如果内容包含乱码字符，尝试修复
  if (processedContent.includes('�') || /[\u00C0-\u00FF]{2,}/.test(processedContent)) {
    // 尝试修复常见的编码问题
    try {
      // 处理一些常见的编码错误
      processedContent = processedContent
        .replace(/â€™/g, "'")
        .replace(/â€œ/g, '"')
        .replace(/â€/g, '"')
        .replace(/â€¦/g, '...')
        .replace(/Â/g, '')
        .replace(/â/g, '')
    } catch (e) {
      console.warn('字符编码修复失败:', e)
    }
  }
  
  // 简单的 HTML 清理 - 移除潜在危险的标签和属性
  let cleanContent = processedContent
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
  
  // 处理纯文本内容（如果没有HTML标签）
  if (!cleanContent.includes('<') && !cleanContent.includes('>')) {
    // 将纯文本转换为HTML段落，保留换行
    cleanContent = cleanContent
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(line => `<p>${escapeHtml(line)}</p>`)
      .join('')
  }
  
  // 如果内容为空，显示默认消息
  if (!cleanContent.trim()) {
    return '<p style="color: #94a3b8; font-style: italic;">此邮件无内容或内容无法显示</p>'
  }
  
  return cleanContent
}

// HTML转义函数
const escapeHtml = (text) => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}
</script>

<style scoped>
/* 组件特定样式 */
.mail-meta-item .meta-value .badge {
  margin-right: var(--spacing-xs);
}

/* 邮件内容样式改进 */
.mail-content {
  line-height: 1.6;
  color: var(--text-primary);
  word-wrap: break-word;
  font-size: 14px;
  max-width: 100%;
}

/* 邮件内容中的HTML元素样式 */
.mail-content h1, .mail-content h2, .mail-content h3, 
.mail-content h4, .mail-content h5, .mail-content h6 {
  margin: 1rem 0 0.5rem 0;
  font-weight: 600;
  color: var(--text-primary);
}

.mail-content h1 { font-size: 1.5rem; }
.mail-content h2 { font-size: 1.3rem; }
.mail-content h3 { font-size: 1.1rem; }
.mail-content h4, .mail-content h5, .mail-content h6 { font-size: 1rem; }

.mail-content p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.mail-content ul, .mail-content ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.mail-content li {
  margin: 0.25rem 0;
}

.mail-content blockquote {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid var(--primary-color);
  background-color: #f8fafc;
  font-style: italic;
}

.mail-content pre {
  background-color: #f4f4f4;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  margin: 0.5rem 0;
}

.mail-content code {
  background-color: #f4f4f4;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.mail-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 0.5rem 0;
}

.mail-content a {
  color: var(--primary-color);
  text-decoration: underline;
}

.mail-content a:hover {
  color: var(--primary-hover);
}

.mail-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.mail-content th, .mail-content td {
  border: 1px solid var(--border-color);
  padding: 0.5rem;
  text-align: left;
}

.mail-content th {
  background-color: #f8fafc;
  font-weight: 600;
}

/* 处理长文本换行 */
.mail-content {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* 邮件引用样式 */
.mail-content .gmail_quote,
.mail-content .yahoo_quoted,
.mail-content .outlook_quote {
  border-left: 3px solid #ccc;
  padding-left: 1rem;
  margin: 1rem 0;
  color: var(--text-muted);
}

/* 邮件签名样式 */
.mail-content .signature {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
  font-size: 13px;
}
</style>