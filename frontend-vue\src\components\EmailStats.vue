<template>
  <div class="email-stats-panel">
    <div class="stats-header">
      <h3 class="stats-title">📊 邮件统计</h3>
      <button class="btn btn-small btn-secondary" @click="refreshStats">刷新</button>
    </div>
    
    <div class="stats-content">
      <!-- 总体统计 -->
      <div class="stat-group">
        <h4 class="group-title">总体统计</h4>
        <div class="stat-grid">
          <div class="stat-item">
            <div class="stat-value">{{ totalEmails }}</div>
            <div class="stat-label">总邮件数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ totalAccounts }}</div>
            <div class="stat-label">账户数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ todayEmails }}</div>
            <div class="stat-label">今日邮件</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ weekEmails }}</div>
            <div class="stat-label">本周邮件</div>
          </div>
        </div>
      </div>

      <!-- 账户统计 -->
      <div class="stat-group">
        <h4 class="group-title">账户邮件分布</h4>
        <div class="account-stats">
          <div 
            v-for="accountStat in accountStats" 
            :key="accountStat.account"
            class="account-stat-item"
          >
            <div class="account-info">
              <span class="account-email">{{ accountStat.account }}</span>
              <span class="account-count">{{ accountStat.count }}封</span>
            </div>
            <div class="account-bar">
              <div 
                class="account-bar-fill" 
                :style="{ width: `${(accountStat.count / maxAccountEmails) * 100}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热门发件人 -->
      <div class="stat-group">
        <h4 class="group-title">热门发件人 (TOP 10)</h4>
        <div class="sender-stats">
          <div 
            v-for="(senderStat, index) in topSenders" 
            :key="senderStat.sender"
            class="sender-stat-item"
          >
            <div class="sender-rank">{{ index + 1 }}</div>
            <div class="sender-info">
              <div class="sender-name" :title="senderStat.sender">{{ senderStat.sender }}</div>
              <div class="sender-count">{{ senderStat.count }}封邮件</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间分布 -->
      <div class="stat-group">
        <h4 class="group-title">时间分布</h4>
        <div class="time-stats">
          <div class="time-chart">
            <div 
              v-for="day in last7Days" 
              :key="day.date"
              class="day-bar"
              :title="`${day.date}: ${day.count}封邮件`"
            >
              <div 
                class="day-bar-fill" 
                :style="{ height: `${(day.count / maxDayEmails) * 100}%` }"
              ></div>
              <div class="day-label">{{ day.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  emails: {
    type: Array,
    default: () => []
  },
  accounts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['refresh'])

// 基础统计
const totalEmails = computed(() => props.emails.length)
const totalAccounts = computed(() => props.accounts.length)

// 今日邮件统计
const todayEmails = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  return props.emails.filter(email => {
    const emailDate = new Date(email.date)
    return emailDate >= today
  }).length
})

// 本周邮件统计
const weekEmails = computed(() => {
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)
  weekAgo.setHours(0, 0, 0, 0)
  
  return props.emails.filter(email => {
    const emailDate = new Date(email.date)
    return emailDate >= weekAgo
  }).length
})

// 账户统计
const accountStats = computed(() => {
  const stats = {}
  
  props.emails.forEach(email => {
    const account = email.recipient || email.email_address || '未知账户'
    stats[account] = (stats[account] || 0) + 1
  })
  
  return Object.entries(stats)
    .map(([account, count]) => ({ account, count }))
    .sort((a, b) => b.count - a.count)
})

const maxAccountEmails = computed(() => {
  return Math.max(...accountStats.value.map(stat => stat.count), 1)
})

// 热门发件人统计
const topSenders = computed(() => {
  const stats = {}
  
  props.emails.forEach(email => {
    const sender = email.sender || '未知发件人'
    // 提取邮箱地址
    const emailMatch = sender.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
    const cleanSender = emailMatch ? emailMatch[1] : sender
    
    stats[cleanSender] = (stats[cleanSender] || 0) + 1
  })
  
  return Object.entries(stats)
    .map(([sender, count]) => ({ sender, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)
})

// 最近7天统计
const last7Days = computed(() => {
  const days = []
  const today = new Date()
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    date.setHours(0, 0, 0, 0)
    
    const nextDay = new Date(date)
    nextDay.setDate(nextDay.getDate() + 1)
    
    const count = props.emails.filter(email => {
      const emailDate = new Date(email.date)
      return emailDate >= date && emailDate < nextDay
    }).length
    
    days.push({
      date: date.toISOString().split('T')[0],
      label: i === 0 ? '今天' : i === 1 ? '昨天' : date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }),
      count
    })
  }
  
  return days
})

const maxDayEmails = computed(() => {
  return Math.max(...last7Days.value.map(day => day.count), 1)
})

// 刷新统计
const refreshStats = () => {
  emit('refresh')
}
</script>

<style scoped>
.email-stats-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  height: fit-content;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.stats-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.stats-content {
  padding: 16px;
  max-height: 600px;
  overflow-y: auto;
}

.stat-group {
  margin-bottom: 24px;
}

.stat-group:last-child {
  margin-bottom: 0;
}

.group-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
}

/* 总体统计网格 */
.stat-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 账户统计 */
.account-stats {
  space-y: 8px;
}

.account-stat-item {
  margin-bottom: 8px;
}

.account-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.account-email {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.account-count {
  font-size: 12px;
  color: var(--text-secondary);
}

.account-bar {
  height: 6px;
  background: var(--bg-secondary);
  border-radius: 3px;
  overflow: hidden;
}

.account-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  transition: width 0.3s ease;
}

/* 发件人统计 */
.sender-stats {
  max-height: 200px;
  overflow-y: auto;
}

.sender-stat-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.sender-stat-item:last-child {
  border-bottom: none;
}

.sender-rank {
  width: 24px;
  height: 24px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.sender-info {
  flex: 1;
  min-width: 0;
}

.sender-name {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sender-count {
  font-size: 11px;
  color: var(--text-secondary);
}

/* 时间分布图表 */
.time-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 80px;
  padding: 0 4px;
}

.day-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin: 0 2px;
}

.day-bar-fill {
  width: 100%;
  min-height: 2px;
  background: linear-gradient(180deg, var(--primary-color), var(--primary-color-light));
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
  margin-bottom: 4px;
}

.day-label {
  font-size: 10px;
  color: var(--text-secondary);
  text-align: center;
  white-space: nowrap;
}

/* 响应式 */
@media (max-width: 1200px) {
  .stat-grid {
    grid-template-columns: 1fr;
  }
  
  .account-email {
    max-width: 120px;
  }
}
</style>