<template>
  <section class="email-list-panel">
    <div class="status-bar">
      <h2 class="section-title">📋 邮件列表</h2>
      <div class="status-info">
        <span :class="['status-message', `status-${statusType}`]">{{ statusMessage }}</span>
      </div>
    </div>

    <!-- 筛选和搜索栏 -->
    <div class="filter-bar">
      <div class="filter-row">
        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            type="text" 
            v-model="searchKeyword" 
            placeholder="搜索邮件主题或发件人..."
            class="search-input"
            @input="handleSearch"
          >
          <span class="search-icon">🔍</span>
        </div>

        <!-- 日期筛选 -->
        <select v-model="dateFilter" @change="handleFilterChange" class="filter-select">
          <option value="">所有日期</option>
          <option value="today">今天</option>
          <option value="yesterday">昨天</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
        </select>

        <!-- 排序 -->
        <select v-model="sortBy" @change="handleFilterChange" class="filter-select">
          <option value="date-desc">最新优先</option>
          <option value="date-asc">最旧优先</option>
          <option value="sender-asc">发件人A-Z</option>
          <option value="sender-desc">发件人Z-A</option>
          <option value="subject-asc">主题A-Z</option>
          <option value="subject-desc">主题Z-A</option>
        </select>
      </div>

      <div class="filter-row">
        <!-- 批量操作 -->
        <div class="batch-actions" v-if="selectedEmails.length > 0">
          <span class="batch-info">已选择 {{ selectedEmails.length }} 封邮件</span>
          <button class="btn btn-small btn-secondary" @click="selectAll">全选</button>
          <button class="btn btn-small btn-secondary" @click="clearSelection">清空选择</button>
          <button class="btn btn-small btn-primary" @click="exportSelected">导出选中</button>
        </div>

        <!-- 分页信息 -->
        <div class="pagination-info">
          显示 {{ startIndex + 1 }}-{{ endIndex }} 条，共 {{ totalEmails }} 条
          (第{{ currentPage }}/{{ totalPages }}页，每页{{ pageSize }}条)
        </div>
      </div>
    </div>
    
    <div class="email-list-container">
      <div class="email-list">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner">🔄</div>
          <div class="loading-title">正在获取邮件...</div>
        </div>
        
        <div v-else-if="filteredEmails.length === 0" class="empty-state">
          <div class="empty-icon">📭</div>
          <div class="empty-title">{{ emails.length === 0 ? '未找到邮件' : '没有符合条件的邮件' }}</div>
          <div class="empty-description">{{ emails.length === 0 ? '请检查选择的账户和文件夹' : '请尝试调整筛选条件' }}</div>
        </div>
        
        <div
          v-else
          v-for="(email, index) in paginatedEmails"
          :key="index"
          :class="['email-item', { selected: selectedEmailIndex === index }]"
          @click="handleEmailClick(email, index)"
        >
          <!-- 复选框 -->
          <div class="email-checkbox" @click.stop>
            <input 
              type="checkbox" 
              :checked="selectedEmails.includes(email.id || `${email.email_address}-${index}`)"
              @change="toggleEmailSelection(email.id || `${email.email_address}-${index}`)"
              class="checkbox-input"
            >
          </div>
          <div class="email-header">
            <div class="email-subject" :title="email.subject">
              📧 {{ email.subject }}
            </div>
            <div class="email-date">
              📅 {{ formatDate(email.date) }}
            </div>
          </div>
          <div class="email-from" :title="email.sender">
            👤 {{ email.sender }}
          </div>
          <div class="email-badges">
            <span class="badge badge-account">{{ getEmailFromSender(email.recipient) }}</span>
            <span class="badge badge-folder">📁 {{ email.folder }}</span>
          </div>
        </div>
      </div>

      <!-- 分页控件 -->
      <div class="pagination-controls" v-if="filteredEmails.length > 0">
        <button 
          class="btn btn-small btn-secondary" 
          :disabled="currentPage === 1"
          @click="goToPage(currentPage - 1)"
        >
          ⬅️ 上一页
        </button>
        
        <div class="page-numbers">
          <button 
            v-for="page in visiblePages" 
            :key="page"
            :class="['btn', 'btn-small', page === currentPage ? 'btn-primary' : 'btn-secondary']"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          class="btn btn-small btn-secondary" 
          :disabled="currentPage === totalPages"
          @click="goToPage(currentPage + 1)"
        >
          下一页 ➡️
        </button>

        <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
          <option value="10">10条/页</option>
          <option value="25">25条/页</option>
          <option value="50">50条/页</option>
          <option value="100">100条/页</option>
        </select>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  emails: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  statusMessage: {
    type: String,
    default: '📭 请选择账户和文件夹，然后点击"获取邮件"'
  },
  statusType: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'success', 'warning', 'error'].includes(value)
  }
})

const emit = defineEmits(['email-selected', 'batch-operation'])

// 筛选和排序状态
const searchKeyword = ref('')
const dateFilter = ref('')
const sortBy = ref('date-desc')
const selectedEmails = ref([])
const selectedEmailIndex = ref(null)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(25)

// 搜索防抖
let searchTimeout = null
const handleSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1 // 搜索时重置到第一页
  }, 300)
}

// 筛选邮件
const filteredEmails = computed(() => {
  let filtered = [...props.emails]

  // 关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(email => 
      email.subject?.toLowerCase().includes(keyword) ||
      email.sender?.toLowerCase().includes(keyword)
    )
  }

  // 日期筛选
  if (dateFilter.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    filtered = filtered.filter(email => {
      const emailDate = new Date(email.date)
      
      switch (dateFilter.value) {
        case 'today':
          return emailDate >= today
        case 'yesterday':
          const yesterday = new Date(today)
          yesterday.setDate(yesterday.getDate() - 1)
          return emailDate >= yesterday && emailDate < today
        case 'week':
          const weekAgo = new Date(today)
          weekAgo.setDate(weekAgo.getDate() - 7)
          return emailDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today)
          monthAgo.setMonth(monthAgo.getMonth() - 1)
          return emailDate >= monthAgo
        default:
          return true
      }
    })
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'date-desc':
        return new Date(b.date) - new Date(a.date)
      case 'date-asc':
        return new Date(a.date) - new Date(b.date)
      case 'sender-asc':
        return (a.sender || '').localeCompare(b.sender || '')
      case 'sender-desc':
        return (b.sender || '').localeCompare(a.sender || '')
      case 'subject-asc':
        return (a.subject || '').localeCompare(b.subject || '')
      case 'subject-desc':
        return (b.subject || '').localeCompare(a.subject || '')
      default:
        return 0
    }
  })

  return filtered
})

// 分页计算
const totalEmails = computed(() => filteredEmails.value.length)
const totalPages = computed(() => Math.ceil(totalEmails.value / pageSize.value))
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value)
const endIndex = computed(() => Math.min(startIndex.value + pageSize.value, totalEmails.value))

const paginatedEmails = computed(() => {
  return filteredEmails.value.slice(startIndex.value, endIndex.value)
})

// 可见页码
const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) pages.push(i)
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) pages.push(i)
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) pages.push(i)
      pages.push('...')
      pages.push(total)
    }
  }
  
  return pages.filter(p => p !== '...' || pages.indexOf(p) === pages.lastIndexOf(p))
})

// 处理邮件点击
const handleEmailClick = (email, index) => {
  selectedEmailIndex.value = index
  emit('email-selected', email, index)
}

const handleFilterChange = () => {
  currentPage.value = 1 // 筛选改变时重置到第一页
}

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const handlePageSizeChange = () => {
  pageSize.value = parseInt(pageSize.value) // 确保pageSize是数字
  currentPage.value = 1 // 改变页面大小时重置到第一页
}

// 批量操作
const toggleEmailSelection = (emailId) => {
  const index = selectedEmails.value.indexOf(emailId)
  if (index > -1) {
    selectedEmails.value.splice(index, 1)
  } else {
    selectedEmails.value.push(emailId)
  }
}

const selectAll = () => {
  selectedEmails.value = paginatedEmails.value.map(email => 
    email.id || `${email.email_address}-${paginatedEmails.value.indexOf(email)}`
  )
}

const exportSelected = () => {
  const selectedEmailData = filteredEmails.value.filter(email => 
    selectedEmails.value.includes(email.id || `${email.email_address}-${filteredEmails.value.indexOf(email)}`)
  )
  
  // 生成CSV内容
  const csvContent = [
    ['主题', '发件人', '收件人', '日期', '文件夹'].join(','),
    ...selectedEmailData.map(email => [
      `"${email.subject || ''}"`,
      `"${email.sender || ''}"`,
      `"${email.recipient || ''}"`,
      `"${email.date || ''}"`,
      `"${email.folder || ''}"`
    ].join(','))
  ].join('\n')
  
  // 下载文件
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `邮件导出_${new Date().toISOString().split('T')[0]}.csv`
  link.click()
  
  // 清空选择
  clearSelection()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return dateString
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

// 从发件人字符串中提取邮箱地址
const getEmailFromSender = (sender) => {
  if (!sender) return ''
  
  const emailMatch = sender.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
  return emailMatch ? emailMatch[1] : sender
}

// 清空选中状态
const clearSelection = () => {
  selectedEmailIndex.value = null
  selectedEmails.value = []
}

// 监听邮件变化，重置选择和分页
watch(() => props.emails, () => {
  selectedEmails.value = []
  currentPage.value = 1
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  clearSelection
})
</script>

<style scoped>
/* 邮件列表面板 */
.email-list-panel {
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  height: 100%;
}

/* 筛选栏 */
.filter-bar {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

/* 搜索框 */
.search-box {
  position: relative;
  flex: 1;
  max-width: 200px;
}

.search-input {
  width: 100%;
  padding: 6px 30px 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 12px;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.search-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: var(--text-secondary);
  pointer-events: none;
}

/* 筛选选择框 */
.filter-select {
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 12px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* 批量操作 */
.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.batch-info {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
}

/* 分页信息 */
.pagination-info {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
}

/* 邮件列表容器 */
.email-list-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.email-list {
  flex: 1;
  overflow-y: auto;
}

/* 邮件项 */
.email-item {
  position: relative;
  padding: 12px 12px 12px 32px; /* 为复选框留出空间 */
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.email-item:hover {
  background: var(--bg-hover);
}

.email-item.selected {
  background: var(--primary-color-light);
  border-left: 3px solid var(--primary-color);
}

/* 邮件项复选框 */
.email-checkbox {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.checkbox-input {
  width: 14px;
  height: 14px;
  cursor: pointer;
}

/* 分页控件 */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 12px;
  border-top: 2px solid var(--border-color);
  background: var(--bg-secondary);
  flex-shrink: 0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-numbers .btn {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.page-size-select {
  padding: 4px 6px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 11px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  min-height: 200px;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
  animation: spin 1s linear infinite;
}

.loading-title {
  font-size: 1rem;
  color: var(--text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  min-height: 200px;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.status-info {
  color: var(--text-secondary);
}

.status-success {
  color: var(--success-color);
}

.status-warning {
  color: var(--warning-color);
}

.status-error {
  color: var(--error-color);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>