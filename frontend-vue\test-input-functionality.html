<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Functionality Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .clipboard-textarea {
            min-height: 150px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
            white-space: pre-wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            margin-right: 10px;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .debug-info {
            margin-top: 20px;
            padding: 16px;
            background: #f3f4f6;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.8rem;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 4px 0;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>Email Import Input Functionality Test</h1>
            
            <h2>Manual Input Fields</h2>
            <div class="form-group">
                <label class="form-label">Email Address</label>
                <input 
                    v-model="manualForm.email" 
                    type="email" 
                    class="form-input" 
                    placeholder="<EMAIL>"
                    @input="onEmailInput"
                    @focus="onEmailFocus"
                    @blur="onEmailBlur"
                >
            </div>
            
            <div class="form-group">
                <label class="form-label">Client ID</label>
                <input 
                    v-model="manualForm.clientId" 
                    type="text" 
                    class="form-input" 
                    placeholder="Application Client ID"
                    @input="onClientIdInput"
                >
            </div>
            
            <div class="form-group">
                <label class="form-label">Refresh Token</label>
                <textarea 
                    v-model="manualForm.refreshToken" 
                    class="form-input form-textarea" 
                    placeholder="Refresh Token"
                    @input="onRefreshTokenInput"
                ></textarea>
            </div>
            
            <h2>Clipboard Input</h2>
            <div class="form-group">
                <label class="form-label">Paste Account Data</label>
                <textarea
                    v-model="clipboardText"
                    class="form-input form-textarea clipboard-textarea"
                    placeholder="Paste account data here...

Supported formats:
1. One account per line: email,client_id,refresh_token
2. JSON array format
3. Tab-separated data"
                    @paste="onPaste"
                    @input="onClipboardInput"
                ></textarea>
            </div>
            
            <button class="btn btn-primary" @click="testValidation">Test Validation</button>
            <button class="btn btn-primary" @click="clearAll">Clear All</button>
            
            <div class="debug-info">
                <h3>Debug Information</h3>
                <div class="status" :class="inputStatus.email.working ? 'success' : 'error'">
                    Email Input: {{ inputStatus.email.working ? 'Working' : 'Not Working' }}
                    (Value: "{{ manualForm.email }}")
                </div>
                <div class="status" :class="inputStatus.clientId.working ? 'success' : 'error'">
                    Client ID Input: {{ inputStatus.clientId.working ? 'Working' : 'Not Working' }}
                    (Value: "{{ manualForm.clientId }}")
                </div>
                <div class="status" :class="inputStatus.refreshToken.working ? 'success' : 'error'">
                    Refresh Token Input: {{ inputStatus.refreshToken.working ? 'Working' : 'Not Working' }}
                    (Length: {{ manualForm.refreshToken.length }})
                </div>
                <div class="status" :class="inputStatus.clipboard.working ? 'success' : 'error'">
                    Clipboard Input: {{ inputStatus.clipboard.working ? 'Working' : 'Not Working' }}
                    (Length: {{ clipboardText.length }})
                </div>
                
                <h4>Event Log:</h4>
                <div v-for="(event, index) in eventLog" :key="index">
                    {{ event }}
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;
        
        createApp({
            setup() {
                const manualForm = reactive({
                    email: '',
                    clientId: '',
                    refreshToken: ''
                });
                
                const clipboardText = ref('');
                const eventLog = ref([]);
                
                const inputStatus = reactive({
                    email: { working: false },
                    clientId: { working: false },
                    refreshToken: { working: false },
                    clipboard: { working: false }
                });
                
                const logEvent = (message) => {
                    const timestamp = new Date().toLocaleTimeString();
                    eventLog.value.unshift(`[${timestamp}] ${message}`);
                    if (eventLog.value.length > 10) {
                        eventLog.value.pop();
                    }
                };
                
                const onEmailInput = (event) => {
                    inputStatus.email.working = true;
                    logEvent(`Email input: "${event.target.value}"`);
                };
                
                const onEmailFocus = () => {
                    logEvent('Email field focused');
                };
                
                const onEmailBlur = () => {
                    logEvent('Email field blurred');
                };
                
                const onClientIdInput = (event) => {
                    inputStatus.clientId.working = true;
                    logEvent(`Client ID input: "${event.target.value}"`);
                };
                
                const onRefreshTokenInput = (event) => {
                    inputStatus.refreshToken.working = true;
                    logEvent(`Refresh Token input: ${event.target.value.length} characters`);
                };
                
                const onPaste = (event) => {
                    inputStatus.clipboard.working = true;
                    logEvent('Paste event detected in clipboard area');
                    
                    // Get clipboard data
                    const clipboardData = event.clipboardData || window.clipboardData;
                    const pastedData = clipboardData.getData('text');
                    logEvent(`Pasted data length: ${pastedData.length} characters`);
                };
                
                const onClipboardInput = (event) => {
                    inputStatus.clipboard.working = true;
                    logEvent(`Clipboard input: ${event.target.value.length} characters`);
                };
                
                const testValidation = () => {
                    logEvent('Testing validation...');
                    
                    if (manualForm.email) {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        const isValid = emailRegex.test(manualForm.email);
                        logEvent(`Email validation: ${isValid ? 'VALID' : 'INVALID'}`);
                    }
                    
                    if (clipboardText.value) {
                        logEvent(`Clipboard content ready for parsing (${clipboardText.value.length} chars)`);
                    }
                };
                
                const clearAll = () => {
                    manualForm.email = '';
                    manualForm.clientId = '';
                    manualForm.refreshToken = '';
                    clipboardText.value = '';
                    eventLog.value = [];
                    
                    // Reset status
                    Object.keys(inputStatus).forEach(key => {
                        inputStatus[key].working = false;
                    });
                    
                    logEvent('All fields cleared');
                };
                
                return {
                    manualForm,
                    clipboardText,
                    eventLog,
                    inputStatus,
                    onEmailInput,
                    onEmailFocus,
                    onEmailBlur,
                    onClientIdInput,
                    onRefreshTokenInput,
                    onPaste,
                    onClipboardInput,
                    testValidation,
                    clearAll
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
