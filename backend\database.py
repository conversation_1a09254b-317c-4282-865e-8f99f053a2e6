"""
数据库操作模块
负责 SQLite 数据库的连接、表创建和所有 CRUD 操作
"""

import os
import sqlite3
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from models import AccountCreate, AccountDetail

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库文件路径
DATABASE_PATH = os.environ.get('DATABASE_PATH', "accounts.db")


@contextmanager
def get_db_connection():
    """数据库连接上下文管理器"""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"数据库操作错误: {e}")
        raise
    finally:
        if conn:
            conn.close()


def init_db():
    """初始化数据库，创建accounts表并确保包含session_id字段"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # 创建accounts表（如果不存在）- 支持会话隔离
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email_address TEXT NOT NULL,
            client_id TEXT NOT NULL,
            refresh_token TEXT NOT NULL,
            status TEXT DEFAULT 'OK',
            last_updated TEXT NOT NULL,
            session_id TEXT NOT NULL,
            UNIQUE(email_address, session_id)
        )
    ''')

    # 检查是否需要添加session_id列（兼容旧数据库）
    cursor.execute("PRAGMA table_info(accounts)")
    columns = [column[1] for column in cursor.fetchall()]

    if 'session_id' not in columns:
        cursor.execute('ALTER TABLE accounts ADD COLUMN session_id TEXT')
        logger.info("已添加session_id列到accounts表")

    conn.commit()
    conn.close()
    logger.info("数据库初始化完成")


def add_account(account_data: AccountCreate, session_id: str = None) -> AccountDetail:
    """
    添加新的邮箱账户

    Args:
        account_data: 账户创建数据
        session_id: 会话ID，用于数据隔离

    Returns:
        AccountDetail: 创建的账户详细信息

    Raises:
        sqlite3.IntegrityError: 如果邮箱地址已存在
    """
    current_time = datetime.now().isoformat()

    insert_sql = """
    INSERT INTO accounts (email_address, client_id, refresh_token, status, last_updated, session_id)
    VALUES (?, ?, ?, ?, ?, ?)
    """

    with get_db_connection() as conn:
        cursor = conn.execute(
            insert_sql,
            (account_data.email_address, account_data.client_id,
             account_data.refresh_token, 'OK', current_time, session_id)
        )
        account_id = cursor.lastrowid
        conn.commit()

        logger.info(f"成功添加账户: {account_data.email_address} (会话: {session_id})")

        return AccountDetail(
            id=account_id,
            email_address=account_data.email_address,
            client_id=account_data.client_id,
            refresh_token=account_data.refresh_token,
            status='OK',
            last_updated=current_time
        )


def get_account_by_email(email_address: str, session_id: str = None) -> Optional[AccountDetail]:
    """
    根据邮箱地址获取账户信息（支持会话隔离）

    Args:
        email_address: 邮箱地址
        session_id: 会话ID，如果提供则只查询该会话的账户

    Returns:
        AccountDetail 或 None
    """
    if session_id:
        select_sql = """
        SELECT id, email_address, client_id, refresh_token, status, last_updated
        FROM accounts WHERE email_address = ? AND session_id = ?
        """
        params = (email_address, session_id)
    else:
        # 兼容旧代码，不指定session_id时查询所有
        select_sql = """
        SELECT id, email_address, client_id, refresh_token, status, last_updated
        FROM accounts WHERE email_address = ?
        """
        params = (email_address,)

    with get_db_connection() as conn:
        cursor = conn.execute(select_sql, params)
        row = cursor.fetchone()

        if row:
            return AccountDetail(
                id=row['id'],
                email_address=row['email_address'],
                client_id=row['client_id'],
                refresh_token=row['refresh_token'],
                status=row['status'],
                last_updated=row['last_updated']
            )
        return None


def get_all_accounts(session_id: str = None) -> List[Dict[str, Any]]:
    """
    获取账户信息（不包含 refresh_token）

    Args:
        session_id: 会话ID，如果提供则只返回该会话的账户

    Returns:
        List[Dict]: 账户列表
    """
    if session_id:
        select_sql = """
        SELECT id, email_address, client_id, status, last_updated
        FROM accounts WHERE session_id = ? ORDER BY last_updated DESC
        """
        params = (session_id,)
    else:
        # 兼容旧代码，不指定session_id时返回所有账户
        select_sql = """
        SELECT id, email_address, client_id, status, last_updated
        FROM accounts ORDER BY last_updated DESC
        """
        params = ()

    with get_db_connection() as conn:
        cursor = conn.execute(select_sql, params)
        rows = cursor.fetchall()

        return [dict(row) for row in rows]


def delete_account(email_address: str, session_id: str = None) -> bool:
    """
    删除指定邮箱的账户（支持会话隔离）

    Args:
        email_address: 邮箱地址
        session_id: 会话ID，如果提供则只删除该会话的账户

    Returns:
        bool: 是否成功删除
    """
    if session_id:
        delete_sql = "DELETE FROM accounts WHERE email_address = ? AND session_id = ?"
        params = (email_address, session_id)
    else:
        # 兼容旧代码，不指定session_id时删除所有匹配的账户
        delete_sql = "DELETE FROM accounts WHERE email_address = ?"
        params = (email_address,)

    with get_db_connection() as conn:
        cursor = conn.execute(delete_sql, params)
        conn.commit()

        if cursor.rowcount > 0:
            logger.info(f"成功删除账户: {email_address} (会话: {session_id})")
            return True
        else:
            logger.warning(f"账户不存在: {email_address} (会话: {session_id})")
            return False


def update_account_status(email_address: str, status: str, session_id: str = None) -> bool:
    """
    更新账户状态（支持会话隔离）

    Args:
        email_address: 邮箱地址
        status: 新状态
        session_id: 会话ID，如果提供则只更新该会话的账户

    Returns:
        bool: 是否成功更新
    """
    current_time = datetime.now().isoformat()

    if session_id:
        update_sql = """
        UPDATE accounts
        SET status = ?, last_updated = ?
        WHERE email_address = ? AND session_id = ?
        """
        params = (status, current_time, email_address, session_id)
    else:
        # 兼容旧代码，不指定session_id时更新所有匹配的账户
        update_sql = """
        UPDATE accounts
        SET status = ?, last_updated = ?
        WHERE email_address = ?
        """
        params = (status, current_time, email_address)

    with get_db_connection() as conn:
        cursor = conn.execute(update_sql, params)
        conn.commit()

        if cursor.rowcount > 0:
            logger.info(f"更新账户状态: {email_address} -> {status} (会话: {session_id})")
            return True
        return False


def get_accounts_by_session(session_id: str) -> List[Dict[str, Any]]:
    """
    获取指定会话的账户（完整信息，包含refresh_token）

    Args:
        session_id: 会话ID

    Returns:
        List[Dict]: 账户列表
    """
    select_sql = """
    SELECT id, email_address, client_id, refresh_token, status, last_updated, session_id
    FROM accounts WHERE session_id = ? ORDER BY last_updated DESC
    """

    with get_db_connection() as conn:
        cursor = conn.execute(select_sql, (session_id,))
        rows = cursor.fetchall()

        return [dict(row) for row in rows]
