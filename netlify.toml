[build]
  # 基础目录 - 指向frontend子目录
  base = "frontend-vue"
  # 构建命令
  command = "npm run build"
  # 构建输出目录（相对于base目录）
  publish = "dist"

[build.environment]
  # Node.js版本
  NODE_VERSION = "20"
  # API配置
  VITE_API_BASE_URL = "https://outlook.jx099.com"

# SPA路由支持 - 重要！
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 安全头设置
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# 静态资源缓存
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
